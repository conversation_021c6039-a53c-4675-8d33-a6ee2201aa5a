# 序列号管理功能设计文档

## 1. 系统概述

序列号管理功能旨在为ETC设备建立完整的生命周期追踪体系，记录设备从入库、出库、发货到寄回的全流程数据。该功能基于现有的etc-java-delivery微服务架构，扩展库存管理模块，实现设备序列号的精确追踪和状态管理。

### 核心业务价值
- **设备追踪**：实现ETC设备全生命周期的精确追踪
- **状态管控**：确保设备状态变更的准确性和一致性  
- **质量管理**：支持设备质量问题的快速定位和处理
- **库存优化**：提供精确的库存数据，支持库存预警和补货决策

### 系统范围
- 序列号注册与管理
- 入库/出库流程中的序列号关联
- 发货过程中的序列号绑定
- 寄回流程中的序列号验证和状态更新

## 2. 技术架构

### 系统分层架构
基于现有的四层架构模式：Controller → Business → Service → Entity

```mermaid
graph TD
    A[Controller层] --> B[Business层]
    B --> C[Service层]
    C --> D[Entity层]
    
    A1[SerialNumberController] --> B1[SerialNumberBusiness]
    A1 --> B2[SerialNumberBusiness]
    
    B1 --> C1[SerialNumberService]
    B2 --> C2[SerialNumberTraceService]
    
    C1 --> D1[SerialNumber]
    C2 --> D2[SerialNumberTrace]
    C1 --> D3[SerialNumberStock]
```

### 核心类设计
基于项目现有的BaseEntity和BaseService模式：

```mermaid
classDiagram
    class BaseEntity {
        <<abstract>>
        +Model~T~ extends Model~?~
    }
    
    class BaseService {
        <<abstract>>
        +M extends CommonBaseMapper~T~
        +T extends BaseEntity~T~
    }
    
    class Serial {
        +Integer id
        +String serialNo
        +String storageCode
        +String storageSku
        +Byte inventoryType
        +Byte status
        +LocalDateTime createdAt
        +LocalDateTime updatedAt
    }
    
    class SerialService {
        +Serial getBySerialNo(String)
        +List~Serial~ getByStorageCode(String)
        +void updateStatus(String, Byte)
        +void batchCreate(List~Serial~)
    }
    
    BaseEntity <|-- Serial
    BaseService <|-- SerialService
    SerialService --> Serial
```

## 3. 数据模型设计

### 核心实体关系
基于项目现有结构，序列号管理功能使用以下实体设计：

```mermaid
erDiagram
    etc_serial {
        int id PK
        varchar serial_no UK "序列号"
        varchar storage_code "仓库编码"
        varchar storage_sku "仓库SKU"
        tinyint inventory_type "库存类型[1-良品 2-次品]"
        tinyint status "状态[1-在库 2-出库 3-发货 4-退回]"
        datetime created_at
        datetime updated_at
    }
    
    etc_serial_log {
        int id PK
        varchar serial_no FK "序列号"
        tinyint status "状态[1-在库 2-出库 3-发货 4-退回]"
        varchar business_source "业务来源"
        varchar business_sn "业务单号"
        varchar operator "操作人"
        varchar remark "备注"
        datetime created_at
        datetime updated_at
    }
    
    etc_stock_goods_serial {
        int id PK
        varchar stock_sn "出入库关联单号"
        int stock_goods_id "出入库商品记录id"
        varchar serial_no "序列号"
        datetime created_at
        datetime updated_at
    }
    
    etc_logistics_sku_serial {
        int id PK
        varchar logistics_sn "发货流水号"
        int logistics_sku_id "发货商品记录id"
        varchar serial_no "序列号"
        datetime created_at
        datetime updated_at
    }
    
    etc_storage_record_serial {
        int id PK
        varchar record_sn "入库记录单号"
        varchar serial_no "序列号"
        datetime created_at
        datetime updated_at
    }
    
    etc_storage {
        int id PK
        varchar storage_code UK
        varchar name
        varchar area
        varchar address
    }
    
    etc_storage_record {
        int id PK
        varchar record_sn UK "入库记录单号"
        varchar storage_code "仓库编码"
        varchar express_number "快递单号"
        int goods_type "货物类型[1-ETC卡+OBU 2-ETC单卡 等]"
        varchar goods_images "货物图片"
        int nums "寄回数量"
        varchar sku_info "SKU信息(JSON)"
        varchar operator "操作人"
        int status "状态[1-待确认 2-匹配成功 3-匹配失败]"
        datetime revice_time "接收时间"
        datetime created_at
        datetime updated_at
    }
    
    etc_serial ||--o{ etc_serial_log : "追踪记录"
    etc_serial ||--o{ etc_stock_goods_serial : "入库关联"
    etc_serial ||--o{ etc_logistics_sku_serial : "发货关联"
    etc_serial ||--o{ etc_storage_record_serial : "寄回关联"
    etc_serial }o--|| etc_storage : "所属仓库"
    etc_storage_record ||--o{ etc_storage_record_serial : "包含序列号"
```

### 状态枚举定义

#### 序列号状态 (SerialStatusEnum)
基于现有实体字段定义：
```mermaid
stateDiagram-v2
    [*] --> IN_STOCK : 序列号入库
    IN_STOCK --> OUT_STOCK : 出库
    OUT_STOCK --> SHIPPED : 发货
    SHIPPED --> RETURNED : 寄回
    RETURNED --> IN_STOCK : 重新入库
```

#### 库存类型 (InventoryTypeEnum)
- GOOD (1): 良品
- DEFECTIVE (2): 次品

#### 业务来源 (BusinessSourceEnum)
- STOCK_IN: 入库业务
- STOCK_OUT: 出库业务
- LOGISTICS: 物流发货
- SENDBACK: 寄回业务

## 4. 核心业务流程

### 序列号自动生成流程
基于韵达入库确认接口自动生成序列号记录：
```mermaid
sequenceDiagram
    participant Yunda as 韵达仓库
    participant YundaController as YundaController
    participant StockNotifyBusiness as StockNotifyBusiness
    participant SerialBusiness as SerialBusiness
    participant SerialService as SerialService
    participant SerialLogService as SerialLogService
    participant DB as 数据库
    
    Yunda->>YundaController: /notify/yunda/entryOrderConfirm
    Note right of Yunda: 入库确认通知，包含snList
    YundaController->>StockNotifyBusiness: stockInNotify(StockInConfirmDTO)
    StockNotifyBusiness->>StockNotifyBusiness: 验证入库单状态
    StockNotifyBusiness->>SerialBusiness: handleSerialNumbers(orderLines)
    loop 每个orderLine中的snList
        SerialBusiness->>SerialService: createOrUpdateSerial(sn, stockInSn)
        SerialService->>DB: INSERT/UPDATE etc_serial
        SerialBusiness->>SerialLogService: createLog(sn, STOCK_IN)
        SerialLogService->>DB: INSERT etc_serial_log
    end
    SerialBusiness-->>StockNotifyBusiness: 序列号处理完成
    StockNotifyBusiness-->>YundaController: 返回成功响应
    YundaController-->>Yunda: StockResponseVO
```

### 韵达入库确认接口集成

#### 接口路径
```
POST /notify/yunda/entryOrderConfirm
Content-Type: application/xml
```

#### 请求数据结构
基于现有的StockInConfirmDTO结构：
```xml
<request>
    <entryOrder>
        <entryOrderCode>LR20240115001</entryOrderCode>
        <status>FULFILLED</status>
        <operateTime>2024-01-15 10:30:00</operateTime>
        <outBizCode>BIZ001</outBizCode>
        <!-- 其他入库单信息 -->
    </entryOrder>
    <orderLines>
        <orderLine>
            <itemCode>ETC-DEVICE-001</itemCode>
            <itemName>ETC电子标签</itemName>
            <inventoryType>ZP</inventoryType>
            <actualQty>100</actualQty>
            <snList>
                <sn>1234567890123456</sn>
                <sn>1234567890123457</sn>
                <!-- 更多序列号 -->
            </snList>
        </orderLine>
    </orderLines>
</request>
```

### 韵达出库确认接口集成

#### 接口路径
```
POST /notify/yunda/stockOutConfirm
Content-Type: application/xml
```

#### 请求数据结构
基于现有的StockOutConfirmDTO结构：
```xml
<request>
    <deliveryOrder>
        <deliveryOrderCode>CR20240116001</deliveryOrderCode>
        <status>DELIVERED</status>
        <orderConfirmTime>2024-01-16 14:20:00</orderConfirmTime>
        <outBizCode>BIZ002</outBizCode>
        <expressCode>YD1234567890</expressCode>
        <!-- 其他出库单信息 -->
    </deliveryOrder>
    <orderLines>
        <orderLine>
            <itemCode>ETC-DEVICE-001</itemCode>
            <itemName>ETC电子标签</itemName>
            <inventoryType>ZP</inventoryType>
            <actualQty>50</actualQty>
            <snList>
                <sn>1234567890123456</sn>
                <sn>1234567890123457</sn>
                <!-- 更多序列号 -->
            </snList>
        </orderLine>
    </orderLines>
</request>
```

#### 序列号处理逻辑
1. **接收出库确认**：YundaController接收韵达推送的出库确认
2. **提取序列号**：从orderLines中的snList提取所有序列号
3. **更新状态**：将序列号状态从IN_STOCK(1)更新为OUT_STOCK(2)
4. **记录日志**：创建STOCK_OUT业务来源的SerialLog记录
5. **关联出库单**：通过StockGoodsSerial表关联到具体的出库商品记录

### 韵达发货确认接口集成

#### 接口路径
```
POST /notify/yunda/deliveryConfirm
Content-Type: application/xml
```

#### 请求数据结构
基于现有的DeliveryConfirmDTO结构：
```xml
<request>
    <deliveryOrder>
        <deliveryOrderCode>WL20240117001</deliveryOrderCode>
        <status>DELIVERED</status>
        <orderConfirmTime>2024-01-17 16:30:00</orderConfirmTime>
        <outBizCode>BIZ003</outBizCode>
        <expressCode>YD9876543210</expressCode>
        <!-- 其他发货单信息 -->
    </deliveryOrder>
    <orderLines>
        <orderLine>
            <itemCode>ETC-DEVICE-001</itemCode>
            <itemName>ETC电子标签</itemName>
            <inventoryType>ZP</inventoryType>
            <actualQty>30</actualQty>
            <snList>
                <sn>1234567890123456</sn>
                <sn>1234567890123457</sn>
                <!-- 更多序列号 -->
            </snList>
        </orderLine>
    </orderLines>
    <packages>
        <package>
            <expressCode>YD9876543210</expressCode>
            <logisticsCode>YUNDA</logisticsCode>
        </package>
    </packages>
</request>
```

#### 序列号处理逻辑
1. **接收发货确认**：YundaController接收韵达推送的发货确认
2. **提取序列号**：从orderLines中的snList提取所有序列号
3. **更新状态**：将序列号状态从OUT_STOCK(2)更新为SHIPPED(3)
4. **记录日志**：创建LOGISTICS业务来源的SerialLog记录
5. **关联发货单**：通过LogisticsSkuSerial表关联到具体的发货商品记录

### 序列号自动状态更新流程
基于韵达接口通知自动更新序列号状态：

#### 出库流程
```mermaid
sequenceDiagram
    participant Yunda as 韵达仓库
    participant YundaController as YundaController
    participant StockNotifyBusiness as StockNotifyBusiness
    participant SerialBusiness as SerialBusiness
    participant SerialService as SerialService
    participant SerialLogService as SerialLogService
    participant DB as 数据库
    
    Yunda->>YundaController: /notify/yunda/stockOutConfirm
    Note right of Yunda: 出库确认通知，包含snList
    YundaController->>StockNotifyBusiness: stockOutNotify(StockOutConfirmDTO)
    StockNotifyBusiness->>StockNotifyBusiness: 验证出库单状态
    StockNotifyBusiness->>SerialBusiness: handleStockOutSerials(orderLines)
    loop 每个orderLine中的snList
        SerialBusiness->>SerialService: updateSerialStatus(sn, OUT_STOCK)
        SerialService->>DB: UPDATE etc_serial SET status = 2
        SerialBusiness->>SerialLogService: createLog(sn, STOCK_OUT)
        SerialLogService->>DB: INSERT etc_serial_log
    end
    SerialBusiness-->>StockNotifyBusiness: 序列号处理完成
    StockNotifyBusiness-->>YundaController: 返回成功响应
    YundaController-->>Yunda: StockResponseVO
```

#### 发货流程
```mermaid
sequenceDiagram
    participant Yunda as 韵达仓库
    participant YundaController as YundaController
    participant LogisticsBusiness as LogisticsBusiness
    participant SerialBusiness as SerialBusiness
    participant SerialService as SerialService
    participant SerialLogService as SerialLogService
    participant DB as 数据库
    
    Yunda->>YundaController: /notify/yunda/deliveryConfirm
    Note right of Yunda: 发货确认通知，包含snList
    YundaController->>LogisticsBusiness: orderConfirm(LogisticsOrderConfirmDTO)
    LogisticsBusiness->>LogisticsBusiness: 验证发货单状态
    LogisticsBusiness->>SerialBusiness: handleDeliverySerials(orderLines)
    loop 每个orderLine中的snList
        SerialBusiness->>SerialService: updateSerialStatus(sn, SHIPPED)
        SerialService->>DB: UPDATE etc_serial SET status = 3
        SerialBusiness->>SerialLogService: createLog(sn, LOGISTICS)
        SerialLogService->>DB: INSERT etc_serial_log
    end
    SerialBusiness-->>LogisticsBusiness: 序列号处理完成
    LogisticsBusiness-->>YundaController: 返回成功响应
    YundaController-->>Yunda: DeliveryConfirmVO
```

#### 寄回流程
寄回流程有两个途径，都通过创建StorageRecord记录触发序列号状态更新：

```mermaid
sequenceDiagram
    participant User as 用户/管理员
    participant Controller as Controller层
    participant StorageRecordBusiness as StorageRecordBusiness
    participant SendBackBusiness as SendBackBusiness
    participant SerialBusiness as SerialBusiness
    participant SerialService as SerialService
    participant SerialLogService as SerialLogService
    participant DB as 数据库
    
    User->>Controller: 寄回操作(途径1或2)
    Note right of User: 途径1: /frontend/storage/create<br/>途径2: /admin/storageRecord/create
    Controller->>StorageRecordBusiness: create(DTO)
    StorageRecordBusiness->>StorageRecordBusiness: 创建StorageRecord记录
    StorageRecordBusiness->>SendBackBusiness: sendBackBindStorage(record)
    SendBackBusiness->>SendBackBusiness: getByExpressNumber(expressNumber)
    SendBackBusiness->>SendBackBusiness: checkAndUpdate(sendBack, storageRecord)
    SendBackBusiness->>SerialBusiness: handleReturnSerials(storageRecord)
    loop 处理寄回的序列号
        SerialBusiness->>SerialService: updateSerialStatus(sn, RETURNED)
        SerialService->>DB: UPDATE etc_serial SET status = 4
        SerialBusiness->>SerialLogService: createLog(sn, SENDBACK)
        SerialLogService->>DB: INSERT etc_serial_log
    end
    SerialBusiness-->>SendBackBusiness: 序列号处理完成
    SendBackBusiness-->>StorageRecordBusiness: 返回处理结果
    StorageRecordBusiness-->>Controller: 返回成功响应
    Controller-->>User: 寄回操作完成
```

### 寄回流程接口集成

#### 签收小程序寄回接口
```
POST /frontend/storage/create
Content-Type: application/json

Request Body (StorageRecordCreateDTO):
{
    "storageCode": "WH001",
    "expressNumber": "YD1234567890",
    "goodsType": 1,
    "goodsImages": "https://example.com/image.jpg",
    "skuInfo": {
        "ETC-DEVICE-001": 2,
        "ETC-DEVICE-002": 1
    },
    "reviceRemark": "设备完好",
    "serialList": [
        "1234567890123456",
        "1234567890123457",
        "1234567890123458"
    ]
}

Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "recordSn": "SR20240118001",
        "isSuccess": true,
        "remark": "检查正常",
        "issuerName": "高灯科技",
        "orderTypeStr": "售后退货"
    }
}
```

#### 后台管理界面寄回接口
```
POST /admin/storageRecord/create
Content-Type: application/json

Request Body (StorageRecordAdminCreateDTO):
{
    "storageCode": "WH001",
    "expressNumber": "YD1234567890",
    "goodsImages": "https://example.com/image.jpg",
    "nums": 3,
    "goodsType": 1,
    "skuInfo": {
        "ETC-DEVICE-001": 2,
        "ETC-DEVICE-002": 1
    },
    "serialList": [
        "1234567890123456",
        "1234567890123457",
        "1234567890123458"
    ]
}

Response:
{
    "code": 0,
    "msg": "success",
    "data": null
}
```

#### 序列号处理逻辑
1. **数据接收**：从请求参数的serialList字段直接接收序列号列表
2. **数据验证**：验证序列号格式(16位数字)和唯一性
3. **状态验证**：确认序列号当前状态为SHIPPED(3)
4. **状态更新**：将序列号状态从SHIPPED(3)更新为RETURNED(4)
5. **关联记录**：通过StorageRecordSerial表关联序列号与寄回记录
6. **日志记录**：创建SENDBACK业务来源的SerialLog记录

## 5. API接口设计

### 序列号管理接口

#### 序列号列表查询
```
POST /admin/serial/list
Content-Type: application/json

Request Body:
{
    "serialNo": "1234567890123456",
    "storageCode": "WH001",
    "storageSku": "ETC-DEVICE-001",
    "status": 1,
    "inventoryType": 1,
    "pageNum": 1,
    "pageSize": 20
}

Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "records": [
            {
                "id": 1,
                "serialNo": "1234567890123456",
                "storageCode": "WH001",
                "storageName": "主仓库",
                "storageSku": "ETC-DEVICE-001",
                "inventoryType": 1,
                "inventoryTypeName": "良品",
                "status": 1,
                "statusName": "在库",
                "createdAt": "2024-01-15 10:30:00",
                "updatedAt": "2024-01-15 10:30:00"
            }
        ],
        "total": 1000,
        "size": 20,
        "current": 1,
        "pages": 50,
        "searchCount": true,
        "hasNext": true,
        "hasPrevious": false
    }
}
```

#### 序列号日志查询
```
POST /admin/serial/log
Content-Type: application/json

Request Body:
{
    "serialNo": "1234567890123456",
    "pageNum": 1,
    "pageSize": 20
}

Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "records": [
            {
                "id": 1,
                "serialNo": "1234567890123456",
                "status": 1,
                "statusName": "在库",
                "businessSource": "STOCK_IN",
                "businessSourceName": "入库业务",
                "businessSn": "LR20240115001",
                "operator": "worker001",
                "remark": "韵达入库确认",
                "createdAt": "2024-01-15 10:30:00"
            },
            {
                "id": 2,
                "serialNo": "1234567890123456",
                "status": 2,
                "statusName": "出库",
                "businessSource": "STOCK_OUT",
                "businessSourceName": "出库业务",
                "businessSn": "CR20240116001",
                "operator": "worker001",
                "remark": "韵达出库确认",
                "createdAt": "2024-01-16 14:20:00"
            }
        ],
        "total": 5,
        "size": 20,
        "current": 1,
        "pages": 1,
        "searchCount": true,
        "hasNext": false,
        "hasPrevious": false
    }
}
```

#### 序列号下拉选项查询
```
GET /admin/serial/getSelectOptions
Content-Type: application/json

Request: 无参数

Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "statusOptions": [
            {
                "value": 1,
                "label": "在库"
            },
            {
                "value": 2,
                "label": "出库"
            },
            {
                "value": 3,
                "label": "发货"
            },
            {
                "value": 4,
                "label": "退回"
            }
        ],
        "inventoryTypeOptions": [
            {
                "value": 1,
                "label": "良品"
            },
            {
                "value": 2,
                "label": "次品"
            }
        ],
        "businessSourceOptions": [
            {
                "value": "STOCK_IN",
                "label": "入库业务"
            },
            {
                "value": "STOCK_OUT",
                "label": "出库业务"
            },
            {
                "value": "LOGISTICS",
                "label": "物流发货"
            },
            {
                "value": "SENDBACK",
                "label": "寄回业务"
            },
            {
                "value": "MANUAL",
                "label": "手动操作"
            }
        ]
    }
}
```

## 6. 业务规则与约束

### 序列号格式规范
- **格式要求**: 16位数字的OBU号码(后续可能调整)
- **示例**: 1234567890123456
- **唯一性**: 全系统唯一，不允许重复
- **长度**: 固定16位数字
- **字符集**: 仅包含数字0-9

### 状态流转规则
1. **在库状态** → **出库状态**: 仅通过韵达/notify/yunda/stockOutConfirm接口
2. **出库状态** → **发货状态**: 仅通过韵达/notify/yunda/deliveryConfirm接口
3. **发货状态** → **寄回状态**: 通过以下两种途径：
   - 签收小程序: /frontend/storage/create接口
   - 后台管理界面: /admin/storageRecord/create接口
4. **寄回状态** → **在库状态**: 重新入库处理(后续扩展)

### 寄回流程特殊规则
- 寄回操作需要先创建LogisticsSendBack记录(寄回件)
- StorageRecord通过expressNumber与LogisticsSendBack关联
- 通过StorageRecordSerial表记录序列号与寄回记录的关联

### 业务约束条件
- 序列号在锁定状态下不能进行出库操作
- 损坏状态的序列号不能进行正常业务流转
- 已报废的序列号不能重新激活使用
- 同一序列号在同一时间只能属于一个仓库

## 7. 数据一致性保障

### 事务处理机制
```mermaid
sequenceDiagram
    participant Business as 业务层
    participant SerialService as 序列号服务
    participant TraceService as 追踪服务
    participant StockService as 库存服务
    participant DB as 数据库
    
    Business->>DB: 开启事务
    Business->>SerialService: 更新序列号状态
    SerialService->>DB: UPDATE serial_number
    Business->>TraceService: 创建操作记录
    TraceService->>DB: INSERT trace_record
    Business->>StockService: 更新库存统计
    StockService->>DB: UPDATE stock_summary
    
    alt 全部成功
        Business->>DB: 提交事务
    else 任一失败
        Business->>DB: 回滚事务
    end
```

### 并发控制策略
- **乐观锁**: 序列号状态更新使用版本号控制
- **悲观锁**: 库存统计更新使用行级锁
- **重试机制**: 并发冲突时自动重试3次
- **幂等性**: 所有状态变更操作支持幂等

## 8. 性能优化方案

### 数据库索引策略
```sql
-- 序列号表主要索引
CREATE INDEX idx_serial_no ON etc_serial_number(serial_no);
CREATE INDEX idx_sku_status ON etc_serial_number(sku_sn, status);
CREATE INDEX idx_storage_status ON etc_serial_number(current_storage_code, status);

-- 追踪表索引
CREATE INDEX idx_trace_serial_time ON etc_serial_number_trace(serial_no, operation_time);
CREATE INDEX idx_trace_business ON etc_serial_number_trace(business_sn);

-- 库存统计表索引
CREATE INDEX idx_stock_storage_sku ON etc_serial_number_stock(storage_code, sku_sn);
```

### 缓存策略
- **序列号状态缓存**: Redis缓存热点序列号状态，TTL 30分钟
- **库存统计缓存**: Redis缓存库存统计数据，TTL 10分钟  
- **查询结果缓存**: 追踪历史查询结果缓存，TTL 5分钟

### 分页优化
- 追踪历史查询默认分页大小20条
- 库存统计查询支持游标分页
- 序列号批量操作限制单次1000条

## 9. 单元测试策略

### 测试覆盖范围
```mermaid
graph TD
    A[单元测试] --> B[Service层测试]
    A --> C[Business层测试]
    A --> D[Controller层测试]
    
    B --> B1[序列号状态变更测试]
    B --> B2[追踪记录创建测试]
    B --> B3[库存统计更新测试]
    
    C --> C1[业务流程测试]
    C --> C2[数据校验测试]
    C --> C3[异常处理测试]
    
    D --> D1[接口参数验证测试]
    D --> D2[响应格式测试]
    D --> D3[权限控制测试]
```

### Mock策略
- **外部依赖Mock**: 物流接口、库存接口Mock
- **数据库Mock**: 使用H2内存数据库
- **Redis Mock**: 使用嵌入式Redis
- **时间Mock**: 固定时间戳确保测试稳定性

### 测试数据准备
- 标准测试序列号池: 1000个预定义序列号
- 多状态测试场景: 覆盖所有状态流转路径
- 异常数据测试: 非法格式、重复序列号等
- 边界值测试: 批量操作边界、并发极限等