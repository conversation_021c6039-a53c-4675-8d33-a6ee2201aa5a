# 序列号流程优化设计文档

## 1. 系统概述

序列号流程优化旨在完善ETC设备在发货、出入库、寄回等业务流程中的序列号处理逻辑，强化序列号与业务单据的关联记录，确保每个序列号在各个业务环节都有完整的追踪记录和状态流转。

### 核心业务价值
- **流程完整性**：确保序列号在发货、出入库、寄回各环节的完整记录
- **数据一致性**：强化序列号状态与业务流程的一致性管理
- **追踪精确性**：通过关联表实现序列号与具体业务记录的精准关联
- **审计可追溯**：提供完整的序列号操作审计链路

### 优化范围
- 优化入库流程中的序列号自动生成和关联逻辑
- 完善出库流程中的序列号状态验证和关联记录
- 改进发货流程中的序列号按SKU分组处理机制
- 增强寄回流程中的序列号验证和关联处理

## 2. 技术架构

### 现有架构基础
基于现有的四层架构模式：Controller → Business → Service → Entity

```mermaid
graph TD
    A[Controller层] --> B[Business层]
    B --> C[Service层]
    C --> D[Entity层]
    
    A1[SerialController] --> B1[SerialBusiness]
    A1[StockNotifyController] --> B2[StockNotifyBusiness]
    A1[StorageRecordController] --> B3[StorageRecordBusiness]
    
    B1 --> C1[SerialService]
    B1 --> C2[SerialLogService]
    B2 --> C3[StockGoodsSerialService]
    B3 --> C4[LogisticsSkuSerialService]
    B3 --> C5[StorageRecordSerialService]
    
    C1 --> D1[Serial]
    C2 --> D2[SerialLog]
    C3 --> D3[StockGoodsSerial]
    C4 --> D4[LogisticsSkuSerial]
    C5 --> D5[StorageRecordSerial]
```

### 核心组件设计
基于项目现有的BaseEntity和BaseService模式，所有序列号相关服务使用@DS("db-delivery")数据源：

```mermaid
classDiagram
    class BaseEntity {
        <<abstract>>
        +Model~T~ extends Model~?~
    }
    
    class BaseService {
        <<abstract>>
        +M extends CommonBaseMapper~T~
        +T extends BaseEntity~T~
        +@DS("db-delivery")
    }
    
    class Serial {
        +Integer id
        +String serialNo
        +String storageCode
        +String storageSku
        +Integer inventoryType
        +Integer status
        +LocalDateTime createdAt
        +LocalDateTime updatedAt
    }
    
    class SerialService {
        +@DS("db-delivery")
        +Serial getBySerialNo(String)
        +void updateStatus(String, Integer)
        +IPage~Serial~ getPage(SerialListDTO)
        +void batchUpdateStatus(List~String~, Integer)
    }
    
    BaseEntity <|-- Serial
    BaseService <|-- SerialService
    SerialService --> Serial
```

## 3. 数据模型优化

### 核心实体关系
基于现有数据模型，优化序列号与业务流程的关联关系：

```mermaid
erDiagram
    etc_serial {
        int id PK
        varchar serial_no UK "序列号"
        varchar storage_code "仓库编码"
        varchar storage_sku "仓库SKU"
        varchar inventory_type "库存类型[ZP-正品 CP-次品]"
        int status "状态[1-在库 2-出库 3-退回]"
        varchar latest_business_source "最新业务来源"
        varchar latest_business_sn "最新业务单号"
        datetime created_at
        datetime updated_at
    }
    
    etc_serial_log {
        int id PK
        varchar serial_no FK "序列号"
        int status "状态[1-在库 2-出库 3-退回]"
        varchar inventory_type "库存类型[ZP-正品 CP-次品]"
        varchar business_source "业务来源"
        varchar business_sn "业务单号"
        varchar operator "操作人"
        varchar remark "备注"
        datetime created_at
        datetime updated_at
    }
    
    etc_stock_goods_serial {
        int id PK
        varchar stock_sn "出入库关联单号"
        int stock_goods_id "出入库商品记录id"
        varchar serial_no "序列号"
        datetime created_at
        datetime updated_at
    }
    
    etc_logistics_sku_serial {
        int id PK
        varchar logistics_sn "发货流水号"
        int logistics_sku_id "发货商品记录id"
        varchar serial_no "序列号"
        datetime created_at
        datetime updated_at
    }
    
    etc_storage_record_serial {
        int id PK
        varchar record_sn "入库记录单号"
        varchar serial_no "序列号"
        datetime created_at
        datetime updated_at
    }
    
    etc_stock_goods_info {
        int id PK
        int type "1入库，2出库"
        varchar stock_sn "关联单号"
        varchar sku_sn "sku编号"
        varchar delivery_goods_code "物流公司的商品编号"
        varchar goods_name "商品名称"
        int apply_count "申请数量"
        int real_count "实际数量"
        varchar number_info "序列号区间信息JSON"
        int status "状态"
        datetime created_at
        datetime updated_at
    }
    
    etc_logistics_sku {
        int id PK
        varchar logistics_sn "发货单流水号"
        varchar sku "货品编号"
        varchar goods_name "商品名称"
        varchar storage_sku "仓库对应sku的编码"
        int nums "发货数量"
        datetime created_at
        datetime updated_at
    }
    
    etc_storage_record {
        int id PK
        varchar record_sn UK "入库记录单号"
        varchar storage_code "仓库编码"
        varchar express_number "快递单号"
        int goods_type "货物类型"
        varchar goods_images "货物图片"
        int nums "寄回数量"
        varchar sku_info "SKU信息JSON"
        varchar operator "操作人"
        int status "状态[1-待确认 2-匹配成功 3-匹配失败]"
        datetime revice_time "接收时间"
        datetime created_at
        datetime updated_at
    }
    
    etc_serial ||--o{ etc_serial_log : "追踪记录"
    etc_serial ||--o{ etc_stock_goods_serial : "出入库关联"
    etc_serial ||--o{ etc_logistics_sku_serial : "发货关联"
    etc_serial ||--o{ etc_storage_record_serial : "寄回关联"
    etc_stock_goods_info ||--o{ etc_stock_goods_serial : "包含序列号"
    etc_logistics_sku ||--o{ etc_logistics_sku_serial : "包含序列号"
    etc_storage_record ||--o{ etc_storage_record_serial : "包含序列号"
```

### 状态流转优化

#### 序列号状态枚举 (SerialStatusEnum)
基于现有实体字段，优化状态流转验证机制：
```mermaid
stateDiagram-v2
    [*] --> IN_STOCK : 韵达入库确认
    IN_STOCK --> OUT_STOCK : 韵达出库确认
    IN_STOCK --> OUT_STOCK : 韵达发货确认
    OUT_STOCK --> RETURNED : 寄回操作
    RETURNED --> IN_STOCK : 重新入库
    
    note right of IN_STOCK : 在库状态(1)
    note right of OUT_STOCK : 出库状态(2)
    note right of RETURNED : 寄回状态(3)
```

#### 业务来源枚举 (BusinessSourceEnum)
**入库业务来源（对应StockInTypeEnum）**：
- CGRK: 采购入库
- DBRK: 调拨入库
- LYRK: 换新入库
- CCRK: 次品入库
- QTRK: 退货入库
- XNRK: 虚拟入库

**出库业务来源（对应StockOutTypeEnum）**：
- DBCK: 调拨出库
- QTCK: 退货出库
- XNCK: 虚拟出库
- PTCK: 普通出库

**发货业务来源（对应LogisticsOrderTypeEnum）**：
- APPLY: 申办
- AFTER_SALES_RETURN: 售后退货
- AFTER_SALES_CANCEL: 售后注销
- AFTER_SALES_MAINTAIN_EXCHANGE: 维修换货
- AFTER_SALES_MAINTAIN_EXCHANGE_APPLY: 维修换货-未激活
- AFTER_SALES_EXCHANGE: 售后换货
- AFTER_SALES_EXCHANGE_APPLY: 售后换货-未激活
- AFTER_SALES_RECALL_EXCHANGE: 召回换货
- REAPPLY: 补办
- JD_APPLY: 商城订单
- SHOP_AFTER_SALES: 商城订单售后
- REISSUE_LOST: 补发-丢件
- REISSUE_HISTORY: 补发-历史发货失败
- REISSUE_REAPPLY: 补发-补办
- REISSUE_MAINTAIN_EXCHANGE: 补发-维修换货
- REISSUE_APPLY: 补发-申办
- OFFLINE: 地推
- UPGRADE: 设备升级
- GOODS: 商品订单
- MANUAL: 手动下单

**退回业务来源（对应SendBackOrderTypeEnum）**：
- RETURN: 退货寄回
- EXCHANGE: 换货寄回
- UPGRADE: 设备升级寄回

#### 业务来源与状态流转对应关系
- **入库状态(IN_STOCK)**: 使用StockInTypeEnum对应的业务来源（CGRK、DBRK、LYRK、CCRK、QTRK、XNRK）
- **出库状态(OUT_STOCK)**: 出库单使用StockOutTypeEnum对应的业务来源（DBCK、QTCK、XNCK、PTCK） 发货单使用LogisticsOrderTypeEnum对应的业务来源
- **退回状态(RETURNED)**: 使用SendBackOrderTypeEnum对应的业务来源（RETURN、EXCHANGE、UPGRADE）

#### 状态验证机制更新
- 序列号不存在时不阻断流程，记录警告并允许创建
- 状态流转不强校验前置状态，记录警告但继续处理
- 所有异常情况记录到SerialLog中，保证业务连续性
- 寄回操作可以从发货状态或出库状态转入，不强制限制

## 4. 核心业务流程优化

### 入库流程序列号处理优化
基于现有韵达入库确认接口，优化序列号自动生成和关联逻辑：
```mermaid
sequenceDiagram
    participant Yunda as 韵达仓库
    participant YundaController as YundaController
    participant StockNotifyBusiness as StockNotifyBusiness
    participant SerialBusiness as SerialBusiness
    participant SerialService as SerialService
    participant SerialLogService as SerialLogService
    participant StockGoodsSerialService as StockGoodsSerialService
    participant DB as 数据库
    
    Yunda->>YundaController: /notify/yunda/entryOrderConfirm
    Note right of Yunda: 入库确认通知，包含snList和inventoryType
    YundaController->>StockNotifyBusiness: stockInNotify(StockInConfirmDTO)
    StockNotifyBusiness->>StockNotifyBusiness: 验证入库单状态
    StockNotifyBusiness->>StockNotifyBusiness: 更新商品实际数量
    loop 每个orderLine中的snList
        StockNotifyBusiness->>SerialBusiness: handleStockInSerials(snList, stockSn, storageCode, itemCode, inventoryType, operator)
        SerialBusiness->>SerialBusiness: validateSerialNumbers(snList)
        SerialBusiness->>SerialService: getBySerialNos(snList)
        Note right of SerialService: 批量查询现有序列号
        SerialService->>DB: SELECT * FROM etc_serial WHERE serial_no IN (...)
        DB-->>SerialService: 返回现有序列号列表
        SerialBusiness->>SerialBusiness: 构建序列号映射表，分离新增和更新数据
        SerialBusiness->>SerialBusiness: 准备批量操作数据(newSerials, updateSerials, serialLogs)
        alt 有新增序列号
            SerialBusiness->>SerialService: saveBatch(newSerials)
            Note right of SerialService: 批量插入新序列号
            SerialService->>DB: BATCH INSERT etc_serial
        end
        alt 有更新序列号
            SerialBusiness->>SerialService: updateBatchById(updateSerials)
            Note right of SerialService: 批量更新现有序列号
            SerialService->>DB: BATCH UPDATE etc_serial
        end
        SerialBusiness->>SerialLogService: saveBatch(serialLogs)
        Note right of SerialLogService: 批量创建日志记录
        SerialLogService->>DB: BATCH INSERT etc_serial_log
        SerialBusiness->>StockGoodsSerialService: createRelation(stockSn, stockGoodsId, snList)
        StockGoodsSerialService->>DB: BATCH INSERT etc_stock_goods_serial
    end
    SerialBusiness-->>StockNotifyBusiness: 序列号批量处理完成
    StockNotifyBusiness-->>YundaController: 返回成功响应
    YundaController-->>Yunda: StockResponseVO
```

### 入库确认接口优化

#### 接口路径
```
POST /notify/yunda/entryOrderConfirm
Content-Type: application/xml
```

#### 序列号处理逻辑优化
基于现有StockNotifyBusiness.stockInNotify方法的处理流程：

1. **接收入库确认**：YundaController接收韵达推送的入库确认
2. **验证入库单**：检查入库单状态和防重复调用机制
3. **更新商品数量**：调用goodsInfoService.updateRealCount更新实际数量
4. **处理序列号**：从orderLine.getSn()获取序列号列表，从orderLine.getInventoryType()获取库存类型
5. **批量处理**：调用serialBusiness.handleStockInSerials批量处理，传入库存类型参数
6. **更新库存类型**：根据韵达通知的inventoryType字段更新序列号记录的inventory_type字段
   - ZP(正品) → GOOD(1)
   - CC(次品) → DEFECTIVE(2)
7. **创建关联**：通过StockGoodsSerial表关联序列号与出入库商品记录
8. **异常处理**：序列号处理失败时记录错误日志但不阻断主流程

#### 数据结构（基于现有DTO）
```xml
<request>
    <entryOrder>
        <entryOrderCode>LR20240115001</entryOrderCode>
        <status>FULFILLED</status>
        <operateTime>2024-01-15 10:30:00</operateTime>
        <outBizCode>BIZ001</outBizCode>
    </entryOrder>
    <orderLines>
        <orderLine>
            <itemCode>ETC-DEVICE-001</itemCode>
            <itemName>ETC电子标签</itemName>
            <inventoryType>ZP</inventoryType>
            <actualQty>100</actualQty>
            <sn>
                <sn>1234567890123456</sn>
                <sn>1234567890123457</sn>
            </sn>
        </orderLine>
    </orderLines>
</request>
```

**库存类型映射规则**：
- `ZP`（正品）→ `inventory_type = 1` (GOOD)
- `CC`（次品）→ `inventory_type = 2` (DEFECTIVE)

### 出库流程序列号处理优化

#### 出库确认接口路径
```
POST /notify/yunda/stockOutConfirm
Content-Type: application/xml
```

#### 序列号处理逻辑优化
基于现有StockNotifyBusiness.stockOutNotify方法的处理流程：

1. **接收出库确认**：YundaController接收韵达推送的出库确认
2. **验证出库单**：检查出库单状态（DELIVERED或PARTDELIVERED）
3. **更新商品数量**：调用goodsInfoService.updateRealCount更新实际数量
4. **处理序列号状态**：从orderLine.getSn()获取序列号列表，从orderLine.getInventoryType()获取库存类型
5. **状态验证**：验证序列号当前状态是否为IN_STOCK(1)
6. **批量更新**：调用serialBusiness.handleStockOutSerials更新状态为OUT_STOCK(2)
7. **更新库存类型**：根据韵达通知的inventoryType字段更新序列号记录的inventory_type字段
   - ZP(正品) → GOOD(1)
   - CC(次品) → DEFECTIVE(2)
8. **记录日志**：创建STOCK_OUT业务来源的SerialLog记录
9. **关联记录**：通过StockGoodsSerial表关联到具体的出库商品记录ID
10. **异常处理**：序列号处理失败时记录错误日志但不阻断主流程

#### 出库流程时序图
```mermaid
sequenceDiagram
    participant Yunda as 韵达仓库
    participant YundaController as YundaController
    participant StockNotifyBusiness as StockNotifyBusiness
    participant SerialBusiness as SerialBusiness
    participant SerialService as SerialService
    participant SerialLogService as SerialLogService
    participant StockGoodsSerialService as StockGoodsSerialService
    participant DB as 数据库
    
    Yunda->>YundaController: /notify/yunda/stockOutConfirm
    Note right of Yunda: 出库确认通知，包含snList和inventoryType
    YundaController->>StockNotifyBusiness: stockOutNotify(StockOutConfirmDTO)
    StockNotifyBusiness->>StockNotifyBusiness: 验证出库单状态
    StockNotifyBusiness->>StockNotifyBusiness: 更新商品实际数量
    loop 每个orderLine中的snList
        StockNotifyBusiness->>SerialBusiness: handleStockOutSerials(snList, stockSn, storageSku, operator, remark, inventoryType)
        SerialBusiness->>SerialBusiness: validateSerialNumbers(snList)
        SerialBusiness->>SerialBusiness: 确定库存类型(inventoryType)
        SerialBusiness->>SerialService: getBySerialNos(snList)
        Note right of SerialService: 批量查询现有序列号
        SerialService->>DB: SELECT * FROM etc_serial WHERE serial_no IN (...)
        DB-->>SerialService: 返回现有序列号列表
        SerialBusiness->>SerialBusiness: 构建序列号映射表，分离存在和不存在的序列号
        SerialBusiness->>SerialBusiness: 准备批量更新和新增数据(updateSerials, newSerials, serialLogs)
        alt 有需要更新的序列号
            SerialBusiness->>SerialService: updateBatchById(updateSerials)
            Note right of SerialService: 批量更新现有序列号状态为OUT_STOCK，同时更新库存类型
            SerialService->>DB: BATCH UPDATE etc_serial SET status = 2, inventory_type = ?
        end
        alt 有不存在的序列号需要新增
            SerialBusiness->>SerialBusiness: 记录预警日志
            Note right of SerialBusiness: WARN: 出库序列号不存在，已新增记录
            SerialBusiness->>SerialService: saveBatch(newSerials)
            Note right of SerialService: 批量插入新序列号，直接设置为OUT_STOCK状态
            SerialService->>DB: BATCH INSERT etc_serial (status = 2, inventory_type = ?)
        end
        SerialBusiness->>SerialLogService: saveBatch(serialLogs)
        Note right of SerialLogService: 批量创建出库日志记录
        SerialLogService->>DB: BATCH INSERT etc_serial_log
        SerialBusiness->>StockGoodsSerialService: createRelation(stockSn, stockGoodsId, snList)
        StockGoodsSerialService->>DB: BATCH INSERT etc_stock_goods_serial
    end
    SerialBusiness-->>StockNotifyBusiness: 序列号批量处理完成
    StockNotifyBusiness-->>YundaController: 返回成功响应
    YundaController-->>Yunda: StockResponseVO
```

#### 数据结构（基于现有DTO）
```xml
<request>
    <deliveryOrder>
        <deliveryOrderCode>CR20240116001</deliveryOrderCode>
        <status>DELIVERED</status>
        <orderConfirmTime>2024-01-16 14:20:00</orderConfirmTime>
        <outBizCode>BIZ002</outBizCode>
        <expressCode>YD1234567890</expressCode>
    </deliveryOrder>
    <orderLines>
        <orderLine>
            <itemCode>ETC-DEVICE-001</itemCode>
            <itemName>ETC电子标签</itemName>
            <inventoryType>ZP</inventoryType>
            <actualQty>50</actualQty>
            <sn>
                <sn>1234567890123456</sn>
                <sn>1234567890123457</sn>
            </sn>
        </orderLine>
    </orderLines>
</request>
```

**库存类型映射规则**：
- `ZP`（正品）→ `inventory_type = 1` (GOOD)
- `CC`（次品）→ `inventory_type = 2` (DEFECTIVE)

### 发货流程序列号处理优化

#### 发货确认接口路径
```
POST /notify/yunda/deliveryConfirm
Content-Type: application/xml
```

#### 序列号按SKU分组处理机制
基于TaskYundaConfirm和TaskErpOrderConfirm中的现有逻辑，优化发货序列号处理：

```mermaid
sequenceDiagram
    participant Yunda as 韵达仓库
    participant YundaController as YundaController
    participant TaskFactory as TaskFactory
    participant TaskYundaConfirm as TaskYundaConfirm
    participant SerialBusiness as SerialBusiness
    participant LogisticsSkuSerialService as LogisticsSkuSerialService
    participant DB as 数据库
    
    Yunda->>YundaController: /notify/yunda/deliveryConfirm
    Note right of Yunda: 发货确认通知，包含按SKU分组的snList
    YundaController->>TaskFactory: 创建TaskYundaConfirm异步任务
    TaskFactory->>TaskYundaConfirm: childExec(taskRecord)
    TaskYundaConfirm->>TaskYundaConfirm: 解析TaskLogisticsConfirmYundaBO
    TaskYundaConfirm->>TaskYundaConfirm: 验证发货单状态
    loop 按SKU分组处理serialListBySku
        TaskYundaConfirm->>SerialBusiness: handleDeliverySerials(serialList, logisticsSn, itemCode)
        SerialBusiness->>SerialBusiness: validateSerialNumbers(serialList)
        SerialBusiness->>SerialBusiness: validateSerialStatus(serialNo, IN_STOCK)
        SerialBusiness->>SerialService: updateStatus(serialNo, SHIPPED)
        SerialBusiness->>SerialLogService: createLog(serialNo, SHIPPED, 发货订单类型枚举, logisticsSn)
        SerialBusiness->>LogisticsSkuSerialService: createRelation(logisticsSn, logisticsSkuId, serialNo)
        LogisticsSkuSerialService->>DB: INSERT etc_logistics_sku_serial
    end
    TaskYundaConfirm-->>TaskFactory: 任务处理完成
```

#### 发货序列号处理逻辑优化
1. **接收发货确认**：YundaController接收韵达推送的发货确认，创建异步任务
2. **解析任务数据**：TaskYundaConfirm解析TaskLogisticsConfirmYundaBO获取序列号分组数据
3. **按SKU分组处理**：从confirmYundaBO.getSerialListBySku()获取Map<String, List<String>>格式的序列号
4. **状态验证**：验证序列号当前状态是否为IN_STOCK(1)
5. **批量更新状态**：调用serialBusiness.handleDeliverySerials更新状态为SHIPPED(3)
6. **更新库存类型**：根据发货确认中的inventoryType字段更新序列号记录的inventory_type字段
   - ZP(正品) → GOOD(1)
   - CC(次品) → DEFECTIVE(2)
7. **记录日志**：创建LOGISTICS业务来源的SerialLog记录
8. **关联发货商品**：通过LogisticsSkuSerial表关联到具体的LogisticsSku记录ID
9. **异常处理**：发货序列号处理失败时记录错误日志，但不影响发货流程

### 寄回流程序列号处理优化

#### 寄回流程双入口处理
基于现有的StorageRecordBusiness，优化两个寄回入口的序列号处理：

```mermaid
sequenceDiagram
    participant User as 用户/管理员
    participant Controller as Controller层
    participant StorageRecordBusiness as StorageRecordBusiness
    participant SendBackBusiness as SendBackBusiness
    participant SerialBusiness as SerialBusiness
    participant StorageRecordSerialService as StorageRecordSerialService
    participant DB as 数据库
    
    alt 签收小程序入口
        User->>Controller: POST /frontend/storage/create
        Note right of User: StorageRecordCreateDTO(含serialList)
    else 后台管理入口  
        User->>Controller: POST /admin/storageRecord/create
        Note right of User: StorageRecordAdminCreateDTO(含serialList)
    end
    
    Controller->>StorageRecordBusiness: create(DTO)
    StorageRecordBusiness->>StorageRecordBusiness: 生成recordSn
    StorageRecordBusiness->>StorageRecordBusiness: 创建StorageRecord记录
    StorageRecordBusiness->>SendBackBusiness: sendBackBindStorage(record)
    SendBackBusiness->>SendBackBusiness: getByExpressNumber(expressNumber)
    SendBackBusiness->>SendBackBusiness: checkAndUpdate(sendBack, storageRecord)
    
    alt 有序列号列表
        StorageRecordBusiness->>SerialBusiness: handleReturnSerials(serialList, recordSn, operator)
        loop 处理每个序列号
            SerialBusiness->>SerialBusiness: validateSerialNumbers(serialList)
            SerialBusiness->>SerialBusiness: 不强校验状态，记录警告
            SerialBusiness->>SerialService: updateStatus(serialNo, RETURNED)
            SerialBusiness->>SerialLogService: createLog(serialNo, RETURNED, 寄回类型枚举, recordSn)
            SerialBusiness->>StorageRecordSerialService: createRelation(recordSn, serialNo)
            StorageRecordSerialService->>DB: INSERT etc_storage_record_serial
        end
    end
    
    StorageRecordBusiness-->>Controller: 返回成功响应
    Controller-->>User: 寄回操作完成
```

#### 寄回接口数据结构

**签收小程序寄回接口**
```
POST /frontend/storage/create
Content-Type: application/json

Request Body (StorageRecordCreateDTO):
{
    "storageCode": "WH001",
    "expressNumber": "YD1234567890",
    "goodsType": 1,
    "goodsImages": "https://example.com/image.jpg",
    "skuInfo": {
        "ETC-DEVICE-001": 2,
        "ETC-DEVICE-002": 1
    },
    "reviceRemark": "设备完好",
    "serialList": [
        "1234567890123456",
        "1234567890123457",
        "1234567890123458"
    ]
}
```

**后台管理界面寄回接口**
```
POST /admin/storageRecord/create
Content-Type: application/json

Request Body (StorageRecordAdminCreateDTO):
{
    "storageCode": "WH001",
    "expressNumber": "YD1234567890",
    "goodsImages": "https://example.com/image.jpg",
    "nums": 3,
    "goodsType": 1,
    "skuInfo": {
        "ETC-DEVICE-001": 2,
        "ETC-DEVICE-002": 1
    },
    "serialList": [
        "1234567890123456",
        "1234567890123457",
        "1234567890123458"
    ]
}
```

## 5. 业务规则与约束

### 序列号格式规范
- **格式要求**: 16位数字的OBU号码(后续可能调整)
- **示例**: 1234567890123456
- **唯一性**: 全系统唯一，不允许重复
- **验证模式**: ^\d{16}$

### 状态流转规则优化
1. **在库状态** → **出库状态**: 仅通过韵达/notify/yunda/stockOutConfirm接口
2. **在库状态** → **发货状态**: 仅通过韵达/notify/yunda/deliveryConfirm接口
3. **发货状态** → **寄回状态**: 通过两种途径：
   - 签收小程序: /frontend/storage/create接口
   - 后台管理界面: /admin/storageRecord/create接口
4. **出库状态** → **寄回状态**: 通过两种途径：
   - 签收小程序: /frontend/storage/create接口
   - 后台管理界面: /admin/storageRecord/create接口
5. **寄回状态** → **在库状态**: 重新入库处理(后续扩展)

**注意**: 出库状态(OUT_STOCK)与发货流程是两个独立的业务分支，两者都可以转入寄回状态。

### 关联记录约束
- 入库序列号必须通过StockGoodsSerial表关联到StockGoodsInfo记录
- 发货序列号必须通过LogisticsSkuSerial表关联到LogisticsSku记录
- 寄回序列号必须通过StorageRecordSerial表关联到StorageRecord记录
- 所有序列号状态变更必须记录到SerialLog表

### 数据源配置约束
所有序列号相关的Service必须配置使用db-delivery数据库：
- SerialService
- SerialLogService  
- StockGoodsSerialService
- LogisticsSkuSerialService
- StorageRecordSerialService

### 序列号管理接口

#### 序列号列表查询
```
POST /admin/serial/get-list
Content-Type: application/json

Request Body:
{
    "serialNo": "1234567890123456",
    "storageCode": "WH001",
    "storageSku": "ETC-DEVICE-001",
    "status": 1,
    "inventoryType": 1,
    "pageNum": 1,
    "pageSize": 20
}

Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "records": [
            {
                "id": 1,
                "serialNo": "1234567890123456",
                "storageCode": "WH001",
                "storageName": "主仓库",
                "storageSku": "ETC-DEVICE-001",
                "inventoryType": 1,
                "inventoryTypeName": "良品",
                "status": 1,
                "statusName": "在库",
                "createdAt": "2024-01-15 10:30:00",
                "updatedAt": "2024-01-15 10:30:00"
            }
        ],
        "total": 1000,
        "size": 20,
        "current": 1,
        "pages": 50
    }
}
```

### 发货管理接口优化

#### 发货详情接口优化
```
POST /admin/logisticsOrder/getDetail
Content-Type: application/json

Request Body:
{
    "logisticsId": 1
}

Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "receiveInfo": {
            "storageCode": "WH001",
            "storageCodeStr": "主仓库",
            "sendName": "张**",
            "sendPhone": "138****5678",
            "sendArea": "北京市朝阳区",
            "sendAddress": "****"
        },
        "goodsInfo": {
            "orderType": "APPLY",
            "orderTypeStr": "申办",
            "orderSource": "ETC",
            "plateNo": "京 A****",
            "goodsList": [
                {
                    "id": 101,
                    "sku": "ETC-DEVICE-001",
                    "goodsName": "ETC电子标签",
                    "storageSku": "ETC-DEVICE-001",
                    "nums": 2,
                    "serialList": [
                        "1234567890123456",
                        "1234567890123457"
                    ]
                },
                {
                    "id": 102,
                    "sku": "ETC-DEVICE-002",
                    "goodsName": "ETC智能标签",
                    "storageSku": "ETC-DEVICE-002",
                    "nums": 1,
                    "serialList": [
                        "1234567890123458"
                    ]
                }
            ]
        },
        "deliveryInfo": {
            "orderSn": "ORD20240115001",
            "createdAt": "2024-01-15 10:30:00",
            "deliveryStatus": 3,
            "deliveryStatusStr": "已发货",
            "deliveryTime": "2024-01-15 14:20:00",
            "expressCorp": "韵达快递",
            "expressNumber": "YD1234567890"
        }
    }
}
```

#### 发货列表接口优化
```
POST /admin/logisticsOrder/getList
Content-Type: application/json

Request Body:
{
    "logisticsSn": "LOG20240115001",
    "orderType": "APPLY",
    "status": 3,
    "expressNumber": "YD1234567890",
    "serialNo": "1234567890123456",
    "startTime": "2024-01-15 00:00:00",
    "endTime": "2024-01-15 23:59:59",
    "pageNum": 1,
    "pageSize": 20
}

Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "records": [
            {
                "id": 1,
                "logisticsSn": "LOG20240115001",
                "orderType": "APPLY",
                "orderTypeName": "申办",
                "expressNumber": "YD1234567890",
                "storageCode": "WH001",
                "status": 3,
                "statusName": "已发货",
                "createdAt": "2024-01-15 10:30:00",
                "updatedAt": "2024-01-15 14:20:00"
            }
        ],
        "total": 100,
        "size": 20,
        "current": 1,
        "pages": 5
    }
}
```

### 出入库管理接口优化

#### 出入库单序列号查询接口
```
POST /admin/stock-goods/get-serial-list
Content-Type: application/json

Request Body:
{
    "stockSn": "LR20240115001",
    "stockGoodsId": 1001,
    "pageNum": 1,
    "pageSize": 20
}

Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "records": [
            {
                "id": 1,
                "serialNo": "1234567890123456"
            },
            {
                "id": 2,
                "serialNo": "1234567890123457"
            },
            {
                "id": 3,
                "serialNo": "1234567890123458"
            }
        ],
        "total": 98,
        "size": 20,
        "current": 1,
        "pages": 5
    }
}
```

#### 序列号日志查询
```
POST /admin/serial/get-log-list
Content-Type: application/json

Request Body:
{
    "serialNo": "1234567890123456",
    "pageNum": 1,
    "pageSize": 20
}

Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "records": [
            {
                "id": 1,
                "serialNo": "1234567890123456",
                "status": 1,
                "statusName": "在库",
                "businessSource": "CGRK",
                "businessSourceName": "采购入库",
                "businessSn": "LR20240115001",
                "operator": "系统",
                "remark": "韵达入库确认",
                "createdAt": "2024-01-15 10:30:00"
            }
        ],
        "total": 5,
        "size": 20,
        "current": 1
    }
}
```

#### 序列号下拉选项查询
```
GET /admin/serial/get-select-options

Response:
{
    "code": 0,
    "msg": "success",
    "data": {
        "statusList": [
            {"value": 1, "label": "在库"},
            {"value": 2, "label": "出库"},
            {"value": 3, "label": "发货"},
            {"value": 4, "label": "退回"}
        ],
        "inventoryTypeList": [
            {"value": 1, "label": "良品"},
            {"value": 2, "label": "次品"}
        ],
        "businessSourceList": [
            {"value": "CGRK", "label": "采购入库"},
            {"value": "DBRK", "label": "调拨入库"},
            {"value": "LYRK", "label": "换新入库"},
            {"value": "CCRK", "label": "次品入库"},
            {"value": "QTRK", "label": "退货入库"},
            {"value": "XNRK", "label": "虚拟入库"},
            {"value": "DBCK", "label": "调拨出库"},
            {"value": "QTCK", "label": "退货出库"},
            {"value": "XNCK", "label": "虚拟出库"},
            {"value": "PTCK", "label": "普通出库"},
            {"value": "APPLY", "label": "申办"},
            {"value": "AFTER_SALES_RETURN", "label": "售后退货"},
            {"value": "AFTER_SALES_CANCEL", "label": "售后注销"},
            {"value": "AFTER_SALES_MAINTAIN_EXCHANGE", "label": "维修换货"},
            {"value": "AFTER_SALES_MAINTAIN_EXCHANGE_APPLY", "label": "维修换货-未激活"},
            {"value": "AFTER_SALES_EXCHANGE", "label": "售后换货"},
            {"value": "AFTER_SALES_EXCHANGE_APPLY", "label": "售后换货-未激活"},
            {"value": "AFTER_SALES_RECALL_EXCHANGE", "label": "召回换货"},
            {"value": "REAPPLY", "label": "补办"},
            {"value": "JD_APPLY", "label": "商城订单"},
            {"value": "SHOP_AFTER_SALES", "label": "商城订单售后"},
            {"value": "REISSUE_LOST", "label": "补发-丢件"},
            {"value": "REISSUE_HISTORY", "label": "补发-历史发货失败"},
            {"value": "REISSUE_REAPPLY", "label": "补发-补办"},
            {"value": "REISSUE_MAINTAIN_EXCHANGE", "label": "补发-维修换货"},
            {"value": "REISSUE_APPLY", "label": "补发-申办"},
            {"value": "OFFLINE", "label": "地推"},
            {"value": "UPGRADE", "label": "设备升级"},
            {"value": "GOODS", "label": "商品订单"},
            {"value": "MANUAL", "label": "手动下单"},
            {"value": "RETURN", "label": "退货寄回"},
            {"value": "EXCHANGE", "label": "换货寄回"},
            {"value": "UPGRADE", "label": "设备升级寄回"}
        ]
    }
}
```

## 7. 数据一致性保障

### 事务处理机制
```mermaid
sequenceDiagram
    participant Business as 业务层
    participant SerialService as 序列号服务
    participant LogService as 日志服务
    participant RelationService as 关联服务
    participant DB as 数据库
    
    Business->>DB: 开启事务
    Business->>SerialService: 更新序列号状态
    SerialService->>DB: UPDATE etc_serial
    Business->>LogService: 创建操作记录
    LogService->>DB: INSERT etc_serial_log
    Business->>RelationService: 创建关联记录
    RelationService->>DB: INSERT 关联表
    
    alt 全部成功
        Business->>DB: 提交事务
    else 任一失败
        Business->>DB: 回滚事务
    end
```

### 容错机制
- **序列号不存在处理**: 记录警告日志，允许自动创建，不阻断业务流程
- **状态不匹配处理**: 记录警告日志，继续执行操作，保证业务连续性
- **关联记录失败**: 序列号状态变更成功，关联记录创建失败时记录错误但不回滚
- **幂等性保证**: 所有状态变更操作支持重复执行

### 数据校验规范
- 序列号格式验证：16位数字格式校验
- 业务单号验证：关联的业务单号必须存在且状态有效
- 操作权限验证：操作人必须有对应的操作权限
- 时间戳验证：创建时间和更新时间的合理性校验

