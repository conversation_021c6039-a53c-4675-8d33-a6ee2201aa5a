package com.ets.delivery;

import com.alibaba.fastjson.JSON;
import com.ets.delivery.application.DeliveryApplication;
import com.ets.delivery.application.app.thirdservice.business.KdBusiness;
import com.ets.delivery.application.app.thirdservice.response.kd.KdExpressQueryVO;
import com.ets.delivery.application.common.bo.express.ExpressBO;
import com.ets.delivery.application.app.thirdservice.response.kd.KdAutoNumberVO;
import com.ets.delivery.application.app.thirdservice.response.kd.KdSubscribeVO;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@SpringBootTest(classes = DeliveryApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "dev")
public class KdApiTest {

    @Autowired
    KdBusiness kdBusiness;

    @Test
    public void subscribe() {
        ExpressBO expressBO = new ExpressBO();
        expressBO.setExpressNumber("JDVC13914940217");
        expressBO.setExpressCompany("jd");
        expressBO.setPhone("***********");
        KdSubscribeVO subscribeVO = kdBusiness.subscribe(expressBO);
        System.out.println(subscribeVO);
    }

    @Test
    public void expressQuery() {
        ExpressBO expressBO = new ExpressBO();
        expressBO.setExpressNumber("JDVC21563733630");
        expressBO.setExpressCompany("jd");
        expressBO.setPhone("***********");
        KdExpressQueryVO queryVO = kdBusiness.expressQuery(expressBO);
        System.out.println(JSON.toJSONString(queryVO));
    }

    @Test
    public void autoNumber() {
        List<KdAutoNumberVO> autoNumberVOList = kdBusiness.autoNumber("JDVC13914940217");
        System.out.println(autoNumberVOList);
    }
}
