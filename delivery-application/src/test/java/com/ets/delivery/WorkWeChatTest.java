package com.ets.delivery;

import com.ets.delivery.application.DeliveryApplication;
import com.ets.delivery.application.app.factory.storage.StorageFactory;
import com.ets.delivery.application.app.job.StockAlarmJob;
import com.ets.delivery.application.app.thirdservice.business.WorkWeChatBusiness;
import com.ets.delivery.application.common.config.WeChatRobotConfig;
import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import com.ets.delivery.application.common.vo.alarm.*;
import com.ets.delivery.application.infra.entity.SupplyGoods;
import com.ets.delivery.application.infra.service.SupplyGoodsService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest(classes = DeliveryApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "dev")
public class WorkWeChatTest {

    @Autowired
    private WeChatRobotConfig weChatRobotConfig;

    @Autowired
    private WorkWeChatBusiness workWeChatBusiness;

    @Autowired
    private SupplyGoodsService supplyGoodsService;

    @Autowired
    private StockAlarmJob stockAlarmJob;

    @Test
    public void sendFile() {
        URL url = this.getClass().getClassLoader().getResource("test.xlsx");
        File file = new File(url.getFile());
        try {
            workWeChatBusiness.sendFile(file, weChatRobotConfig.getStorageAlarmKey());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @Test
    public void sendMarkdown() {
        StringBuilder markdown = new StringBuilder();
        markdown.append("### 测试发送markdown\n")
                .append("><font color=\"info\">绿色字体</font>\n");
        workWeChatBusiness.sendMarkdown(markdown.toString(), weChatRobotConfig.getErpOrderAlarmKey());
    }

    @Test
    public void stockAlarm() {
        String storageCode = StorageCodeEnum.JD_CLOUD.getValue();
        List<StockAlarmExcelVO> stockAlarmList = new ArrayList<>();
        List<LogisticsAlarmExcelVO> logisticsAlarmList = new ArrayList<>();
        List<YundaDailyLogisticsAlarmExcelVO> yundaStockAlarmList = new ArrayList<>();
        List<JdCloudStockAlarmExcelVO> jdCloudStockAlarmList = new ArrayList<>();

        // 获取仓库sku
        List<SupplyGoods> goodsList = supplyGoodsService.getStorageGoodsList(storageCode);

        StorageStockAlarmFileVO storageStockAlarmFileVO = StorageFactory.create(storageCode).stockAlarm(goodsList);
        // 统计韵达仓库存
        if (storageCode.equals(StorageCodeEnum.YUNDA.getValue())) {
            stockAlarmList = storageStockAlarmFileVO.getStockAlarmList();
            logisticsAlarmList = storageStockAlarmFileVO.getLogisticsAlarmList();
            yundaStockAlarmList = storageStockAlarmFileVO.getYundaStockAlarmList();
        }

        // 统计京东仓库存
        if (storageCode.equals(StorageCodeEnum.JD_CLOUD.getValue())) {
            jdCloudStockAlarmList = storageStockAlarmFileVO.getJdCloudStockAlarmList();
        }
        // 生产文件
        stockAlarmJob.createFile(stockAlarmList, logisticsAlarmList, yundaStockAlarmList, jdCloudStockAlarmList);
    }
}
