package com.ets.delivery;

import cn.hutool.core.bean.BeanUtil;
import com.ets.delivery.application.DeliveryApplication;
import com.ets.delivery.application.app.thirdservice.business.YundaBusiness;
import com.ets.delivery.application.app.thirdservice.business.YundaOpenBusiness;
import com.ets.delivery.application.app.thirdservice.request.yunda.*;
import com.ets.delivery.application.app.thirdservice.response.yunda.*;
import com.ets.delivery.application.app.thirdservice.response.yundaOpen.YundaOpenExpressQueryVO;
import com.ets.delivery.application.app.thirdservice.response.yundaOpen.YundaOpenExpressSubscribeVO;
import com.ets.delivery.application.common.bo.yunda.YundaLogisticsSubscribeBO;
import com.ets.delivery.application.common.bo.yunda.YundaSignBO;
import com.ets.delivery.application.common.config.yunda.YundaConfig;
import com.ets.delivery.application.common.consts.yunda.YundaDeliveryOrderOrderTypeEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest(classes = DeliveryApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "dev")
public class YundaApiTest {

    @Autowired
    private YundaConfig yundaConfig;

    @Autowired
    YundaBusiness yundaBusiness;

    @Autowired
    YundaOpenBusiness yundaOpenBusiness;

    @Test
    public void deliveryOrderCreate() {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        YundaDeliveryOrderCreateXmlDTO orderCreateDTO = new YundaDeliveryOrderCreateXmlDTO();
        // 发货单信息
        YundaDeliveryOrderCreateXmlDTO.DeliveryOrder deliveryOrder = new YundaDeliveryOrderCreateXmlDTO.DeliveryOrder();
        deliveryOrder.setDeliveryOrderCode("6921423195698763254");
        deliveryOrder.setSourcePlatformCode("DY");
        deliveryOrder.setOrderType(YundaDeliveryOrderOrderTypeEnum.JYCK.getValue());
        deliveryOrder.setWarehouseCode(yundaConfig.getWarehouseCode());
        deliveryOrder.setCreateTime(timestamp);
        deliveryOrder.setPlaceOrderTime(timestamp);
        deliveryOrder.setOperateTime(timestamp);
        deliveryOrder.setShopNick(yundaConfig.getShopNick());
        deliveryOrder.setLogisticsCode("SF");
        deliveryOrder.setRemark("测试");

        // 联系人信息
        YundaDeliveryOrderCreateXmlDTO.DeliveryOrder.ReceiverInfo receiverInfo = new YundaDeliveryOrderCreateXmlDTO.DeliveryOrder.ReceiverInfo();
        receiverInfo.setName("#OJYE908i#8SUiEyDkRyxL0V/cTXOZIrrluuQfx8IXuvzG9JM06Rl9108cTFqIDhiwQM33PiiEiB6yamHxDjVEUmufrkensuVAZKSgb2fyZ6ovGD9DC1w=*CgwIARCtHBiYJSABKAESPgo8AIUnsaVjirPK2dZwZSyFjLxe1B/88BXWH23TLNACX57IWDeiRXNRF3n0a6szCVJVgxDKYb4/XAU7oXheGgA=#1##");
        receiverInfo.setMobile("$fNo2HW00iVJi4Upzap597L37gOY0gTlzCEtlQYRSVss=$wmdcncrDls3QkWmWnO3BdIM1FFTZfkf1WTjQp51D2gehmy7rRPCyZns5kAlJMlYUHGRhuuOfknfC6kDQ22lwGnggkj94nNgC8gbjOpbEys7iOg==*CgwIARCtHBiYJSABKAESPgo8SLLrTLPAjoPwgcNpw7MGybqKOZX18TyB7nj6sg3Hdri6UwdbtXqLj6Swbh8SG4Xi1vJNTo2dXuYcAV8XGgA=$1$$");
        receiverInfo.setProvince("广东省");
        receiverInfo.setCity("深圳市");
        receiverInfo.setArea("宝安区");
        receiverInfo.setDetailAddress("#DPFlXAMZK/RMatNNRrCs6TC+VHF1DPFlhOzAcd4B/exnuSmeyqvIo1Zee7olU5jMfqmPUQ4u4ywXejen#5zzZHoR1InBLqwrvTdXn01MuzLRCPTork/zT6fgi7X01S7U3Gh0r5mULunBw9iVWS+1rUUyz7AJXZf8Xa4YEpSGakOnXKk+2E3sHmoI8mMYPK8aU7BQi0wo643b1It2/ooZSZLuiNOgcYBPRabIOkENavwHeaJheyOI=*CgwIARCtHBiYJSABKAESPgo8baY6Uqo9tSq4cZQAOudyUV2LZXcoARdvcW5pb0LdN5WJq9XzI87UTgI6atqcOALZ9ItFLan61Zi6+9IJGgA=#1##");
        deliveryOrder.setReceiverInfo(receiverInfo);

        orderCreateDTO.setDeliveryOrder(deliveryOrder);

        // 发货商品
        List<YundaDeliveryOrderCreateXmlDTO.OrderLine> orderLineList = new ArrayList<>();
        YundaDeliveryOrderCreateXmlDTO.OrderLine orderLine = new YundaDeliveryOrderCreateXmlDTO.OrderLine();
        orderLine.setSourceOrderCode("6921423195698763254");
        orderLine.setOwnerCode(yundaConfig.getCustomerId());
        orderLine.setItemCode("wjdps023mj");
        orderLine.setPlanQty(1);
        orderLineList.add(orderLine);
        orderCreateDTO.setOrderLine(orderLineList);

        YundaDeliveryOrderCreateXmlVO orderCreateVO = yundaBusiness.deliveryOrderCreate(orderCreateDTO);
        System.out.println(orderCreateVO);
    }

    @Test
    public void orderCancel() {
        YundaOrderCancelXmlDTO orderCancelDTO = new YundaOrderCancelXmlDTO();
        orderCancelDTO.setOwnerCode(yundaConfig.getCustomerId());
        orderCancelDTO.setOrderCode("6921423195698763254");
        orderCancelDTO.setOrderType(YundaDeliveryOrderOrderTypeEnum.JYCK.getValue());

        YundaOrderCancelXmlVO orderCancelVO = yundaBusiness.orderCancel(orderCancelDTO);
        System.out.println(orderCancelVO);
    }

    @Test
    public void inventoryQuery() {
        YundaInventoryQueryXmlDTO inventoryQueryDTO = new YundaInventoryQueryXmlDTO();
        List<YundaInventoryQueryXmlDTO.criteria> criteriaList = new ArrayList<>();

        YundaInventoryQueryXmlDTO.criteria criteria = new YundaInventoryQueryXmlDTO.criteria();
        criteria.setWarehouseCode(yundaConfig.getWarehouseCode());
        criteria.setOwnerCode(yundaConfig.getCustomerId());
        criteria.setItemCode("GD001");
        criteria.setInventoryType("ZP");
        criteriaList.add(criteria);

        inventoryQueryDTO.setCriteria(criteriaList);
        YundaInventoryQueryXmlVO inventoryQueryVO = yundaBusiness.inventoryQuery(inventoryQueryDTO);
        System.out.println(inventoryQueryVO);
    }

    @Test
    public void logisticsQuery() {
        String expressNumber = "";
        YundaOpenExpressQueryVO queryVO = yundaOpenBusiness.logisticsQuery(expressNumber);
        System.out.println(queryVO.getData());
    }

    @Test
    public void subscribe() {
        YundaLogisticsSubscribeBO subscribeBO = new YundaLogisticsSubscribeBO();

        YundaOpenExpressSubscribeVO subscribeVO = yundaOpenBusiness.subscribe(subscribeBO);
        System.out.println(subscribeVO.getData());
    }

    @Test
    public void stockList() {
        YundaStockListDTO listDTO = new YundaStockListDTO();
        listDTO.setWarehouseCode(yundaConfig.getWarehouseCode());
        listDTO.setOwner(yundaConfig.getCustomerId());
        listDTO.setChannel(yundaConfig.getChannel().toString());
        listDTO.setStartDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        listDTO.setEndDate(LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        YundaStockListVO yundaStockListVO = yundaBusiness.stockList(listDTO);
        System.out.println(yundaStockListVO);
    }

    @Test
    public void deliveryOrderQuery() {
        DeliveryOrderQueryXmlDTO queryXmlDTO = new DeliveryOrderQueryXmlDTO();
//        queryXmlDTO.setStartTime("2024-04-10 00:00:00");
//        queryXmlDTO.setEndTime("2024-04-14 23:59:59");
        queryXmlDTO.setOwnerCode(yundaConfig.getCustomerId());
        queryXmlDTO.setWarehouseCode(yundaConfig.getWarehouseCode());
        queryXmlDTO.setOrderCode("6218606");
        DeliveryOrderQueryVO queryVO = yundaBusiness.deliveryOrderQuery(queryXmlDTO);
        DeliveryOrderQueryXmlVO queryXmlVO = queryVO.getXmlVO();

        DeliveryOrderQueryXmlVO.Order order = queryXmlVO.getOrder().get(0);
        DeliveryConfirmDTO deliveryConfirmDTO = BeanUtil.copyProperties(order, DeliveryConfirmDTO.class);
        System.out.println(queryXmlVO);
        System.out.println(deliveryConfirmDTO);
    }

    @Test
    public void checkSign() {
        // method=taobao.qimen.deliveryorder.confirm&timestamp=2024-04-19 17:35:40&format=xml&app_key=21177924&v=2.0&sign=16B5B0705CD600C5724646B960D0F63D&sign_method=md5&customerId=YWKH000275
        YundaSignBO signBO = new YundaSignBO();
        String body = "<?xml version=\"1.0\" encoding=\"UTF-8\"?><request>    <orderLines>        <orderLine>            <orderLineNo>7484515</orderLineNo>            <inventoryType>ZP</inventoryType>            <planQty>1</planQty>            <ownerCode>YWKH000275</ownerCode>            <actualPrice/>            <itemCode>L001</itemCode>            <batchCode/>            <discountAmount/>            <subSourceCode/>            <actualQty>1</actualQty>            <productDate/>            <itemId>L001</itemId>            <produceCode/>            <itemName>L牌电脑</itemName>            <extCode/>            <orderSourceCode>3855712032538924802</orderSourceCode>            <expireDate/>            <retailPrice/>        </orderLine>        <orderLine>            <orderLineNo>7484514</orderLineNo>            <inventoryType>ZP</inventoryType>            <planQty>1</planQty>            <ownerCode>YWKH000275</ownerCode>            <actualPrice/>            <itemCode>CarPackage2404191456000000330</itemCode>            <batchCode/>            <discountAmount/>            <subSourceCode/>            <actualQty>1</actualQty>            <productDate/>            <itemId>CarPackage2404191456000000330</itemId>            <produceCode/>            <itemName>CarPackage2404191456000000330</itemName>            <extCode/>            <orderSourceCode>3855712032538924802</orderSourceCode>            <expireDate/>            <retailPrice/>        </orderLine>    </orderLines>    <deliveryOrder>        <sourceOrder>0</sourceOrder>        <orderType>JYCK</orderType>        <storageFee/>        <receiver>            <receiverName>毛**</receiverName>            <receiverMobile>***8915</receiverMobile>            <receiverProvince>上海市</receiverProvince>            <receiverArea>青浦区</receiverArea>            <receiverDetailAddress>香**街道**路***弄绿地浦汇中**栋.</receiverDetailAddress>            <receiverCity>(沪)市辖区</receiverCity>        </receiver>        <payTime>2024-04-19 18:09:27</payTime>        <operateTime>2024-04-19 18:12:11</operateTime>        <logisticsCode>POSTB</logisticsCode>        <orderConfirmTime>2024-04-19 18:12:11</orderConfirmTime>        <deliveryOrderCode>6221109</deliveryOrderCode>        <operatorCode/>        <operatorName>系统执行</operatorName>        <warehouseCode>LHC</warehouseCode>        <shopNick>梦落芳华之今天</shopNick>        <payNo>2024041922001119671410037457</payNo>        <sourcePlatformCode>TB</sourcePlatformCode>        <outBizCode>C101388103</outBizCode>        <orderSourceCode>3855712032538924802</orderSourceCode>        <expressCode>21313123123123</expressCode>        <confirmType>0</confirmType>        <arAmount>0.0</arAmount>        <deliveryOrderId>C101388103</deliveryOrderId>        <status>DELIVERED</status>    </deliveryOrder>    <packages>        <package>            <logisticsName/>            <volume>0</volume>            <theoreticalWeight/>            <packageCode>C101388103-1</packageCode>            <expressCode>21313123123123</expressCode>            <length/>            <width/>            <logisticsCode>POSTB</logisticsCode>            <weight/>            <invoiceNo/>            <items>                <item>                    <itemId>L001</itemId>                    <quantity>1</quantity>                    <itemCode>L001</itemCode>                </item>                <item>                    <itemId>CarPackage2404191456000000330</itemId>                    <quantity>1</quantity>                    <itemCode>CarPackage2404191456000000330</itemCode>                </item>            </items>            <height/>        </package>    </packages></request>";
        String method = "taobao.qimen.deliveryorder.confirm";
        String timestamp = "2024-04-22 11:43:57";
        String format = "xml";
        String appKey = "21177924";
        String v = "2.0";
        String sign = "0FE69171AA23F884890F95D3C760E6A3";
        String signMethod = "md5";
        String customerId = "YWKH000275";
        signBO.setMethod(method).setTimestamp(timestamp).setFormat(format).setAppKey(appKey).setV(v).setSign(sign).setSignMethod(signMethod).setCustomerId(customerId).setBody(body);
        yundaBusiness.checkSign(signBO);
    }
}
