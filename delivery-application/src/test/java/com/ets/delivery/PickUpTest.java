package com.ets.delivery;

import com.ets.delivery.application.DeliveryApplication;
import com.ets.delivery.application.app.business.PickUpBusiness;
import com.ets.delivery.application.infra.entity.Pickup;
import com.ets.delivery.application.infra.service.PickupService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest(classes = DeliveryApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "dev")
public class PickUpTest {

    @Autowired
    private PickUpBusiness pickUpBusiness;

    @Autowired
    private PickupService pickupService;

    @Test
    public void queryTraceInfo() {
        String pickUpSn = "2305072036000032389";
        Pickup pickUp = pickupService.getOneByPickUpSn(pickUpSn);
        pickUpBusiness.queryTraceInfo(pickUp);
    }
}
