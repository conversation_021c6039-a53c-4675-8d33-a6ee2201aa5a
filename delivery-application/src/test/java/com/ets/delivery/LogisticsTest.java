package com.ets.delivery;

import com.ets.delivery.application.DeliveryApplication;
import com.ets.delivery.application.app.business.ExpressBusiness;
import com.ets.delivery.application.app.business.LogisticsBusiness;
import com.ets.delivery.application.infra.entity.ExWarehouse;
import com.ets.delivery.application.infra.entity.Logistics;
import com.ets.delivery.application.infra.service.ExWarehouseService;
import com.ets.delivery.application.infra.service.LogisticsService;
import org.apache.commons.lang3.ObjectUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

@SpringBootTest(classes = DeliveryApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "test")
public class LogisticsTest {

    @Autowired
    private LogisticsBusiness logisticsBusiness;

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private ExpressBusiness expressBusiness;

    @Autowired
    private ExWarehouseService exWarehouseService;

    @Test
    public void getLogisticsAverageMap() {
        List<String> goodsList = Arrays.asList("jskcdpsshzy", "EMG4418124705896", "atsgxdps3mj", "EMG4418122940634", "bjkcsl002", "EMG4418178888893", "jsdpscar001", "bjkcsl001", "gxdpscar001", "EMG4418124705908", "EMG4418125154999", "slbjsps3mj", "EMG4418126751953", "gxcar002", "gxcar003", "EMG4418125154615", "bjkcsl003", "jscar001", "EMG4418126751992", "jskcspsshzy01", "tjkcmc001", "EMG441813112202", "EMG4418125154623", "jskcspsshzy02", "atssps3mj", "EMG4418122940630", "tjmchc03", "EMG4418125154777", "nmcar003", "nmmcdps", "EMG4418126751957", "gxcar001", "wjdps023mj", "nmcar002", "EMG4418126751990", "EMG4418126751965", "EMG4418126751937", "EMG4418126751945", "jskcspsshzy03", "jscar003", "EMG4418124705904", "EMG4418122940614", "tjmchc01", "EMG4418122940626", "mcsps3mj", "tjmchc02", "EMG4418124705912", "jacar002", "tjkcmc002", "EMG4418126751961", "EMG4418125154888", "tjkcmc003", "wjdps013mj", "nmcar001", "EMG4418126751991");
        Map<String, Double> logisticsAverageMap = logisticsBusiness.getLogisticsAverageMap("Yunda", goodsList, 30, 0);
        System.out.println(logisticsAverageMap);
    }

    @Test
    public void logisticsQuery() {
        String params = "JdCloud";
        List<ExWarehouse> exWarehouseList = exWarehouseService.getNeedQueryDeliveryList(params, 30);

        exWarehouseList.forEach(exWarehouse -> {
            try {
                // 出库单详情查询
                logisticsBusiness.orderQuery(exWarehouse);

                // 物流轨迹查询
                expressBusiness.expressQuery(exWarehouse);
            } catch (Exception e) {
                System.out.println(e.getMessage());
            }
        });
    }

    @Test
    public void yundaExpressQuery() {
        List<Logistics> logisticsList = logisticsService.getOverTimeNotSignList();

        logisticsList.forEach(logistics -> {
            try {
                ExWarehouse exWarehouse = exWarehouseService.getOneByIsvUuid(logistics.getLogisticsSn());
                if (ObjectUtils.isNotEmpty(exWarehouse)) {
                    expressBusiness.expressQuery(exWarehouse);
                }
            } catch (Throwable e) {
                System.out.println(e.getMessage());
            }
        });
    }
}
