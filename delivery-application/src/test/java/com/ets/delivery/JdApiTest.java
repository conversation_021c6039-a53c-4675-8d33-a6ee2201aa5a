package com.ets.delivery;

import com.ets.delivery.application.DeliveryApplication;
import com.ets.delivery.application.app.thirdservice.business.JdBusiness;
import com.ets.delivery.application.app.thirdservice.business.JdCloudBusiness;
import com.ets.delivery.application.app.thirdservice.request.jd.*;
import com.ets.delivery.application.app.thirdservice.response.jd.*;
import com.ets.delivery.application.common.config.jd.JdConfig;
import com.ets.delivery.application.common.config.jd.JdExpressConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@SpringBootTest(classes = DeliveryApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "dev")
public class JdApiTest {

    @Autowired
    private JdConfig jdConfig;

    @Autowired
    private JdBusiness jdBusiness;

    @Autowired
    private JdExpressConfig jdExpressConfig;

    @Autowired
    private JdCloudBusiness jdCloudBusiness;

    @Test
    public void standardCalendar() {
        String today = DateTimeFormatter.ofPattern("yyyy-MM-dd").format(LocalDateTime.now());
        JdStandardCalendarDTO standardCalendarDTO = new JdStandardCalendarDTO();
        standardCalendarDTO.setSenderProvince("广东省")
                .setSenderCity("广州市")
                .setSenderDistrict("海珠区")
                .setSenderDetailAddress("广州之窗")
                .setReceiverProvince(jdConfig.getReceiverProvince())
                .setReceiverCity(jdConfig.getReceiverCity())
                .setReceiverDistrict(jdConfig.getReceiverDistrict())
                .setReceiverDetailAddress(jdConfig.getReceiverDetailAddress())
                .setQueryStartDate(today);
        JdStandardCalendarVO standardCalendarVO = jdBusiness.standardCalendar(standardCalendarDTO);
        System.out.println(standardCalendarVO);
    }

    @Test
    public void checkBlinkArea() {
        JdCheckBlinkAreaDTO checkBlinkAreaDTO = new JdCheckBlinkAreaDTO();
        checkBlinkAreaDTO.setSenderProvince("广东省")
                .setSenderCity("广州市")
                .setSenderDistrict("海珠区")
                .setSenderDetailAddress("广州之窗")
                .setReceiverProvince(jdConfig.getReceiverProvince())
                .setReceiverCity(jdConfig.getReceiverCity())
                .setReceiverDistrict(jdConfig.getReceiverDistrict())
                .setReceiverDetailAddress(jdConfig.getReceiverDetailAddress());
        JdCheckBlinkAreaVO checkBlinkAreaVO = jdBusiness.checkBlinkArea(checkBlinkAreaDTO);
        System.out.println(checkBlinkAreaVO);
    }

    @Test
    public void queryFreights() {
        JdQueryEstimatedFreightsDTO queryDTO = new JdQueryEstimatedFreightsDTO();
        queryDTO.setSenderAddress("广东省广州市海珠区广州之窗")
                .setReceiverAddress(
                        jdConfig.getReceiverProvince() +
                        jdConfig.getReceiverCity() +
                        jdConfig.getReceiverDistrict() +
                        jdConfig.getReceiverDetailAddress()).setWeight("0.5");
        JdQueryEstimatedFreightsVO queryVO = jdBusiness.queryFreights(queryDTO);
        System.out.println(queryVO);
    }

    @Test
    public void receivePickUpOrder() {
        JdPickUpOrderCreateDTO pickUpOrderCreateDTO = new JdPickUpOrderCreateDTO();

        JdPickUpOrderCreateVO pickUpOrderCreateVO = jdBusiness.receivePickUpOrder(pickUpOrderCreateDTO);
        System.out.println(pickUpOrderCreateVO);
    }

    @Test
    public void pickUpOrderCancel() {
        JdPickUpOrderCancelDTO pickUpOrderCancelDTO = new JdPickUpOrderCancelDTO();
        pickUpOrderCancelDTO.setPickupCode("")
                .setCustomerCode(jdConfig.getCustomerCode());
        JdPickUpOrderCancelVO pickUpOrderCancelVO = jdBusiness.pickUpCancel(pickUpOrderCancelDTO);
        System.out.println(pickUpOrderCancelVO);
    }

    @Test
    public void matcher() {
        String str = "快递员：杨世梁已出发，手机号：***********，请您准备好全套商品、发票及包装，感谢您的配合！";
        String pattern = "员：([\\u4e00-\\u9fa5]+)已出发，手机号：(\\d+)";

        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(str);
        if (m.find()) {
            System.out.println(m.group(1));
            System.out.println(m.group(2));
        }
    }

    @Test
    public void addOrder() {
        JdAddOrderDTO addOrderDTO = new JdAddOrderDTO();
        addOrderDTO.setIsvUUID("20230630104011");
        addOrderDTO.setShopNo(jdExpressConfig.getShopNo());
        addOrderDTO.setIsvSource(jdExpressConfig.getIsvSource());
        addOrderDTO.setDepartmentNo(jdExpressConfig.getDepartmentNo());
        addOrderDTO.setConsigneeName("测试");
        addOrderDTO.setConsigneeMobile("***********");
        addOrderDTO.setConsigneeAddress("广东省广州市海珠区广州之窗");
        addOrderDTO.setGoodsNo("EMG4418392042964");
        addOrderDTO.setQuantity("1");
        JdAddOrderVO addOrderVO = jdCloudBusiness.addOrder(addOrderDTO);
        System.out.println(addOrderVO);
    }

    @Test
    public void queryOrder() {
        JdQueryOrderDTO queryOrderDTO = new JdQueryOrderDTO();
        queryOrderDTO.setEclpSoNo("ESL92382274220552");
        JdQueryOrderVO queryOrderVO = jdCloudBusiness.queryOrder(queryOrderDTO);
        System.out.println(queryOrderVO);
    }

    @Test
    public void cancelOrder() {
        JdCancelOrderDTO cancelOrderDTO = new JdCancelOrderDTO();
        cancelOrderDTO.setEclpSoNo("ESL92382239352477");
        JdCancelOrderVO cancelOrderVO = jdCloudBusiness.cancelOrder(cancelOrderDTO);
        System.out.println(cancelOrderVO);
    }

    @Test
    public void traceGet() {
        JdExpressTraceGetDTO traceGetDTO = new JdExpressTraceGetDTO();
        traceGetDTO.setWaybillCode("JDVC19992377440");
        traceGetDTO.setCustomerCode(jdConfig.getCustomerCode());
        JdExpressTraceGetVO traceGetVO = jdCloudBusiness.traceGet(traceGetDTO);
        System.out.println(traceGetVO);
    }

    @Test
    public void queryGoodsInfo() {
        JdQueryGoodsInfoDTO queryGoodsInfoDTO = new JdQueryGoodsInfoDTO();
        queryGoodsInfoDTO.setDeptNo(jdExpressConfig.getDepartmentNo());
        queryGoodsInfoDTO.setQueryType("2");
        JdQueryGoodsInfoVO queryGoodsInfoVO = jdCloudBusiness.queryGoodsInfo(queryGoodsInfoDTO);
        System.out.println(queryGoodsInfoVO);
    }

    @Test
    public void queryWarehouse() {
        JdQueryWarehouseDTO queryWarehouseDTO = new JdQueryWarehouseDTO();
        queryWarehouseDTO.setDeptNo(jdExpressConfig.getDepartmentNo());
        JdQueryWarehouseVO queryWarehouseVO = jdCloudBusiness.queryWarehouse(queryWarehouseDTO);
        System.out.println(queryWarehouseVO);
    }

    @Test
    public void queryStock() {
        Arrays.asList("1").forEach(stockType -> {
            JdQueryStockDTO queryStockDTO = new JdQueryStockDTO();
            queryStockDTO.setDeptNo(jdExpressConfig.getDepartmentNo());
            queryStockDTO.setWarehouseNo("");
            queryStockDTO.setStockType(stockType);
            JdQueryStockVO queryStockVO = jdCloudBusiness.queryStock(queryStockDTO);
            System.out.println(queryStockVO);
        });
    }
}
