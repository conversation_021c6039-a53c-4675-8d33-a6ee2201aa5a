package com.ets.delivery.application;

import com.ets.delivery.application.app.business.serial.SerialBusiness;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.util.Arrays;
import java.util.List;

/**
 * SerialBusiness 测试类
 * 测试序列号处理相关功能
 */
@SpringBootTest
@ActiveProfiles("test")
public class SerialBusinessTest {

    /**
     * 测试被移除序列号的处理逻辑
     * 这个测试用于验证 processRemovedSerials 方法的基本逻辑
     */
    @Test
    public void testProcessRemovedSerialsLogic() {
        // 模拟原有序列号列表
        List<String> originalSerialList = Arrays.asList(
            "1234567890123456", 
            "1234567890123457", 
            "1234567890123458", 
            "1234567890123459"
        );
        
        // 模拟新序列号列表（移除了两个序列号）
        List<String> newSerialList = Arrays.asList(
            "1234567890123456", 
            "1234567890123458"
        );
        
        // 预期被移除的序列号
        List<String> expectedRemovedSerials = Arrays.asList(
            "1234567890123457", 
            "1234567890123459"
        );
        
        // 手动计算被移除的序列号（模拟业务逻辑）
        List<String> actualRemovedSerials = originalSerialList.stream()
                .filter(serialNo -> !newSerialList.contains(serialNo))
                .collect(java.util.stream.Collectors.toList());
        
        // 验证结果
        assert actualRemovedSerials.size() == 2 : "应该有2个被移除的序列号";
        assert actualRemovedSerials.containsAll(expectedRemovedSerials) : "被移除的序列号不匹配";
        
        System.out.println("原有序列号: " + originalSerialList);
        System.out.println("新序列号: " + newSerialList);
        System.out.println("被移除的序列号: " + actualRemovedSerials);
        System.out.println("测试通过：被移除序列号处理逻辑正确");
    }
    
    /**
     * 测试边界情况：原有序列号为空
     */
    @Test
    public void testProcessRemovedSerialsWithEmptyOriginalList() {
        List<String> originalSerialList = Arrays.asList();
        List<String> newSerialList = Arrays.asList("1234567890123456");
        
        List<String> actualRemovedSerials = originalSerialList.stream()
                .filter(serialNo -> !newSerialList.contains(serialNo))
                .collect(java.util.stream.Collectors.toList());
        
        assert actualRemovedSerials.isEmpty() : "原有序列号为空时，不应该有被移除的序列号";
        System.out.println("边界测试通过：原有序列号为空的情况处理正确");
    }
    
    /**
     * 测试边界情况：新序列号为空
     */
    @Test
    public void testProcessRemovedSerialsWithEmptyNewList() {
        List<String> originalSerialList = Arrays.asList("1234567890123456", "1234567890123457");
        List<String> newSerialList = Arrays.asList();
        
        List<String> actualRemovedSerials = originalSerialList.stream()
                .filter(serialNo -> !newSerialList.contains(serialNo))
                .collect(java.util.stream.Collectors.toList());
        
        assert actualRemovedSerials.size() == 2 : "新序列号为空时，所有原有序列号都应该被移除";
        assert actualRemovedSerials.containsAll(originalSerialList) : "应该包含所有原有序列号";
        System.out.println("边界测试通过：新序列号为空的情况处理正确");
    }
    
    /**
     * 测试边界情况：序列号完全相同
     */
    @Test
    public void testProcessRemovedSerialsWithSameLists() {
        List<String> originalSerialList = Arrays.asList("1234567890123456", "1234567890123457");
        List<String> newSerialList = Arrays.asList("1234567890123456", "1234567890123457");

        List<String> actualRemovedSerials = originalSerialList.stream()
                .filter(serialNo -> !newSerialList.contains(serialNo))
                .collect(java.util.stream.Collectors.toList());

        assert actualRemovedSerials.isEmpty() : "序列号完全相同时，不应该有被移除的序列号";
        System.out.println("边界测试通过：序列号完全相同的情况处理正确");
    }

    /**
     * 测试序列号去重逻辑
     */
    @Test
    public void testSerialDeduplication() {
        // 包含重复序列号的列表
        List<String> serialListWithDuplicates = Arrays.asList(
            "1234567890123456",
            "1234567890123457",
            "1234567890123456", // 重复
            "1234567890123458",
            "1234567890123457"  // 重复
        );

        // 手动去重
        List<String> deduplicatedList = serialListWithDuplicates.stream()
                .distinct()
                .collect(java.util.stream.Collectors.toList());

        // 验证去重结果
        assert deduplicatedList.size() == 3 : "去重后应该有3个唯一序列号";
        assert deduplicatedList.contains("1234567890123456") : "应该包含第一个序列号";
        assert deduplicatedList.contains("1234567890123457") : "应该包含第二个序列号";
        assert deduplicatedList.contains("1234567890123458") : "应该包含第三个序列号";

        System.out.println("原始序列号（含重复）: " + serialListWithDuplicates);
        System.out.println("去重后序列号: " + deduplicatedList);
        System.out.println("去重测试通过：重复序列号处理正确");
    }
}
