package com.ets.delivery.application.app.disposer;

import com.ets.delivery.application.app.business.aftersalesreviews.AfterSalesReviewsBusiness;
import com.ets.delivery.application.common.bo.aftersalesreviews.AfterSalesReviewAutoAuditBO;
import com.ets.delivery.application.common.config.AfterSalesReviewAutoAuditConfig;
import com.ets.delivery.application.common.consts.aftersalesreviews.AftersalesReviewsDataTypeEnum;
import com.ets.delivery.application.common.consts.aftersalesreviews.AftersalesReviewsStatusEnum;
import com.ets.delivery.application.infra.entity.aftersalesreview.AftersalesReviews;
import com.ets.delivery.application.infra.entity.aftersalesreview.AftersalesReviewsVehicles;
import com.ets.delivery.application.infra.service.AftersalesReviewsLogService;
import com.ets.delivery.application.infra.service.AftersalesReviewsService;
import com.ets.delivery.application.infra.service.AftersalesReviewsVehiclesService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AfterSalesReviewAutoAuditDisposerTest {

    @Mock
    private AftersalesReviewsService aftersalesReviewsService;

    @Mock
    private AftersalesReviewsVehiclesService aftersalesReviewsVehiclesService;

    @Mock
    private AftersalesReviewsLogService aftersalesReviewsLogService;

    @Mock
    private AfterSalesReviewsBusiness aftersalesReviewsBusiness;

    @Mock
    private AfterSalesReviewAutoAuditConfig autoAuditConfig;

    @InjectMocks
    private AfterSalesReviewAutoAuditDisposer disposer;

    private AfterSalesReviewAutoAuditBO testBO;
    private AftersalesReviews testReview;
    private AftersalesReviewsVehicles reviewData;
    private AftersalesReviewsVehicles orderData;

    @BeforeEach
    void setUp() {
        testBO = new AfterSalesReviewAutoAuditBO();
        testBO.setReviewSn("TEST_REVIEW_SN_001");

        testReview = new AftersalesReviews();
        testReview.setReviewSn("TEST_REVIEW_SN_001");
        testReview.setIssuerId(1);
        testReview.setReviewStatus(AftersalesReviewsStatusEnum.PENDING.getValue());

        reviewData = new AftersalesReviewsVehicles();
        reviewData.setDataType(AftersalesReviewsDataTypeEnum.REVIEW_DATA.getValue());
        reviewData.setPlateNo("苏A12345");
        reviewData.setPlateColor(0);
        reviewData.setOwner("张三");

        orderData = new AftersalesReviewsVehicles();
        orderData.setDataType(AftersalesReviewsDataTypeEnum.ORDER_DATA.getValue());
        orderData.setPlateNo("苏A12345");
        orderData.setPlateColor(0);
        orderData.setOwner("张三");
    }

    @Test
    void testExecute_TotalSwitchOff_ShouldAutoApprove() {
        // 设置总开关关闭
        when(autoAuditConfig.shouldAutoPass(1)).thenReturn(true);
        when(autoAuditConfig.getAutoPassReason(1)).thenReturn("总开关关闭，自动审核通过");
        when(aftersalesReviewsService.getByReviewSn(anyString())).thenReturn(testReview);
        when(aftersalesReviewsVehiclesService.getByReviewSn(anyString()))
                .thenReturn(Arrays.asList(reviewData, orderData));

        // 执行测试
        disposer.execute(testBO);

        // 验证自动审核通过
        verify(aftersalesReviewsService).updateById(any(AftersalesReviews.class));
        verify(aftersalesReviewsLogService).create(any());
        verify(aftersalesReviewsBusiness).sendNotifyCallback(any(AftersalesReviews.class));
    }

    @Test
    void testExecute_IssuerSwitchOff_ShouldAutoApprove() {
        // 设置总开关开启，但卡方开关关闭
        when(autoAuditConfig.shouldAutoPass(1)).thenReturn(true);
        when(autoAuditConfig.getAutoPassReason(1)).thenReturn("发卡方[1]开关关闭，自动审核通过");

        when(aftersalesReviewsService.getByReviewSn(anyString())).thenReturn(testReview);
        when(aftersalesReviewsVehiclesService.getByReviewSn(anyString()))
                .thenReturn(Arrays.asList(reviewData, orderData));

        // 执行测试
        disposer.execute(testBO);

        // 验证自动审核通过
        verify(aftersalesReviewsService).updateById(any(AftersalesReviews.class));
        verify(aftersalesReviewsLogService).create(any());
        verify(aftersalesReviewsBusiness).sendNotifyCallback(any(AftersalesReviews.class));
    }

    @Test
    void testExecute_AllSwitchOn_DataMatch_ShouldAutoApprove() {
        // 设置所有开关开启
        when(autoAuditConfig.shouldAutoPass(1)).thenReturn(false);

        when(aftersalesReviewsService.getByReviewSn(anyString())).thenReturn(testReview);
        when(aftersalesReviewsVehiclesService.getByReviewSn(anyString()))
                .thenReturn(Arrays.asList(reviewData, orderData));

        // 执行测试
        disposer.execute(testBO);

        // 验证数据比对后自动审核通过
        verify(aftersalesReviewsService).updateById(any(AftersalesReviews.class));
        verify(aftersalesReviewsLogService).create(any());
        verify(aftersalesReviewsBusiness).sendNotifyCallback(any(AftersalesReviews.class));
    }

    @Test
    void testExecute_AllSwitchOn_DataMismatch_ShouldNotAutoApprove() {
        // 设置所有开关开启
        when(autoAuditConfig.shouldAutoPass(1)).thenReturn(false);

        // 设置数据不匹配
        orderData.setPlateNo("苏B67890"); // 车牌号不同

        when(aftersalesReviewsService.getByReviewSn(anyString())).thenReturn(testReview);
        when(aftersalesReviewsVehiclesService.getByReviewSn(anyString()))
                .thenReturn(Arrays.asList(reviewData, orderData));

        // 执行测试
        disposer.execute(testBO);

        // 验证不会自动审核通过，只记录日志
        verify(aftersalesReviewsService, never()).updateById(any(AftersalesReviews.class));
        verify(aftersalesReviewsLogService).create(any()); // 只记录不一致日志
        verify(aftersalesReviewsBusiness, never()).sendNotifyCallback(any(AftersalesReviews.class));
    }
}
