package com.ets.delivery;

import com.ets.delivery.application.DeliveryApplication;
import com.ets.delivery.application.common.config.DeliveryConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest(classes = DeliveryApplication.class, webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@ActiveProfiles(value = "dev")
public class SettingTest {

    @Autowired
    private DeliveryConfig deliveryConfig;

    @Test
    public void categoryMap() {
        System.out.println(deliveryConfig.getCategoryMap());
    }
}
