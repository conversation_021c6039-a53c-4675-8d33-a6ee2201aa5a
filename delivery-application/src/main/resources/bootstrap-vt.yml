spring:
  application:
    name: delivery-application
  cloud:
    nacos:
      config:
        server-addr: nacos-dev.etczs.net:8848
        namespace: 0ea80fd3-517e-441d-a5bb-7ec8fa1ebb82
        group: apply
        file-extension: yaml
        extension-configs:
          - data-id: delivery-mysql.yaml
            group: apply
            refresh: true
          - data-id: delivery-config.yaml
            group: apply
            refresh: true
          - data-id: common-starter-config.yaml
            group: apply
            refresh: true
          - data-id: yunda-config.yaml
            group: apply
            refresh: true
          - data-id: kd100-config.yaml
            group: apply
            refresh: true
          - data-id: jd-config.yaml
            group: apply
            refresh: true
          - data-id: apply-cos-tencent.properties
            group: apply