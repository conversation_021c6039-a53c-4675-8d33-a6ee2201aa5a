modulelist=com.baomidou.mybatisplus.extension.p6spy.MybatisPlusLogFactory,com.p6spy.engine.outage.P6OutageFactory
# èªå®ä¹æ¥å¿æå°
logMessageFormat=com.baomidou.mybatisplus.extension.p6spy.P6SpyLogger
#æ¥å¿è¾åºå°æ§å¶å°
appender=com.baomidou.mybatisplus.extension.p6spy.StdoutLogger
# ä½¿ç¨æ¥å¿ç³»ç»è®°å½ sql
#appender=com.p6spy.engine.spy.appender.Slf4JLogger
# è®¾ç½® p6spy driver ä»£ç
deregisterdrivers=true
# åæ¶JDBC URLåç¼
useprefix=true
# éç½®è®°å½ Log ä¾å¤,å¯å»æçç»æéæerror,info,batch,debug,statement,commit,rollback,result,resultset.
excludecategories=info,debug,result,commit,resultset
# æ¥ææ ¼å¼
dateformat=yyyy-MM-dd HH:mm:ss
# å®éé©±å¨å¯å¤ä¸ª
#driverlist=org.h2.Driver
# æ¯å¦å¼å¯æ¢SQLè®°å½
outagedetection=true
# æ¢SQLè®°å½æ å 2 ç§
outagedetectioninterval=2