spring:
  application:
    name: delivery-application
  cloud:
    nacos:
      config:
        server-addr: nacos.public:8848
        namespace: test1
        group: apply
        file-extension: yaml
        extension-configs:
          - data-id: delivery-mysql.yaml
            group: apply
            refresh: true
          - data-id: delivery-config.yaml
            group: apply
            refresh: true
          - data-id: common-starter-config.yaml
            group: apply
            refresh: true
          - data-id: yunda-config.yaml
            group: apply
            refresh: true
          - data-id: kd100-config.yaml
            group: apply
            refresh: true
          - data-id: jd-config.yaml
            group: apply
            refresh: true
          - data-id: apply-cos-tencent.properties
            group: apply