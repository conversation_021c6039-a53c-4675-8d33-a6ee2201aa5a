package com.ets.delivery.application.common.dto.serial;

import com.ets.delivery.application.common.consts.serial.SerialTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 序列号异步处理DTO
 * 用于封装序列号异步处理的参数
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SerialProcessDTO {

    /**
     * 序列号列表
     */
    private List<String> serialList;

    /**
     * 业务单号（发货单号、入库单号等）
     */
    private String businessSn;

    /**
     * 仓库SKU编码
     */
    private String storageSku;

    /**
     * 库存类型（ZP/CC/JS/XS等）
     */
    private String inventoryType;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 备注
     */
    private String remark;

    /**
     * 仓库编码
     */
    private String storageCode;

    /**
     * 业务来源
     */
    private String businessSource;

    /**
     * 序列号处理类型
     */
    private SerialTypeEnum serialType;

    /**
     * 重试次数（用于异步处理失败重试）
     */
    private Integer retryCount = 0;

    /**
     * 最大重试次数
     */
    private Integer maxRetryCount = 3;
}
