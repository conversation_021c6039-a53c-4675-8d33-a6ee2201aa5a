package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
@Data
@TableName("etc_stock_goods_serial")
public class StockGoodsSerial extends BaseEntity<StockGoodsSerial> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 出入库关联单号
     */
    private String stockSn;

    /**
     * 出入库商品记录id
     */
    private Integer stockGoodsId;

    /**
     * 序列号
     */
    private String serialNo;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
