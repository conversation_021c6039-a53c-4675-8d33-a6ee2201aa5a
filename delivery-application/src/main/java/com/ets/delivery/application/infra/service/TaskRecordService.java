package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordStatusEnum;
import com.ets.delivery.application.common.dto.task.TaskRecordDTO;
import com.ets.delivery.application.infra.entity.TaskRecord;
import com.ets.delivery.application.infra.mapper.TaskRecordMapper;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 任务记录 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-11-18
 */
@Service
@DS("db-issuer-admin")
public class TaskRecordService extends BaseService<TaskRecordMapper, TaskRecord> {

    public TaskRecord getOneByCondition(String referSn, String referType, String notifyContent) {
        Wrapper<TaskRecord> wrapper = Wrappers.<TaskRecord>lambdaQuery()
                .eq(TaskRecord::getReferSn, referSn)
                .eq(TaskRecord::getReferType, referType)
                .eq(ObjectUtils.isNotEmpty(notifyContent), TaskRecord::getNotifyContent, notifyContent)
                .last("limit 1")
                .orderByDesc(TaskRecord::getCreatedAt);
        return this.baseMapper.selectOne(wrapper);
    }

    /*
     * 通过taskSn获取
     */
    public TaskRecord getOneByTaskSn(String taskSn) {
        Wrapper<TaskRecord> wrapper = Wrappers.<TaskRecord>lambdaQuery()
            .eq(TaskRecord::getTaskSn, taskSn)
            .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    /*
     * 创建task
     */
    public TaskRecord addNew(TaskRecordDTO taskRecordDTO) {

        TaskRecord taskRecord = new TaskRecord();
        taskRecord.setTaskSn(taskRecordDTO.getTaskSn());
        taskRecord.setReferSn(taskRecordDTO.getReferSn());
        taskRecord.setReferType(taskRecordDTO.getReferType());
        taskRecord.setNotifyContent(taskRecordDTO.getNotifyContent());
        taskRecord.setNextExecTime(taskRecordDTO.getNextExecTime());
        taskRecord.setNotifyContent(taskRecordDTO.getNotifyContent());
        taskRecord.setStatus(taskRecordDTO.getStatus());
        taskRecord.setCreatedAt(LocalDateTime.now());
        taskRecord.setUpdatedAt(LocalDateTime.now());
        this.save(taskRecord);

        return taskRecord;
    }

    /*
     * 获取创建时间之前的数据
     */
    public List<TaskRecord> getListByCreatedAt(Integer minusDays) {
        LambdaQueryWrapper<TaskRecord> wrapper = new QueryWrapper<TaskRecord>().lambda()
                .in(TaskRecord::getStatus, Arrays.asList(
                        TaskRecordStatusEnum.TASK_STATUS_WAIT.getCode(),
                        TaskRecordStatusEnum.TASK_STATUS_FAIL.getCode()
                ))
                .in(TaskRecord::getReferType, TaskRecordReferTypeEnum.getTypeList())
                .gt(TaskRecord::getCreatedAt, LocalDateTime.now().minusDays(minusDays))
                .orderByAsc(TaskRecord::getCreatedAt)
                .last("limit 1000");

        return super.baseMapper.selectList(wrapper);
    }

    public TaskRecord getByReferSn(String referSn, String referType) {

        LambdaQueryWrapper<TaskRecord> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(TaskRecord::getReferSn, referSn)
                .eq(TaskRecord::getReferType, referType);

        return getOneByWrapper(wrapper);
    }
}
