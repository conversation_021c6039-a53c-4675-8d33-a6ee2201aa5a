package com.ets.delivery.application.infra.service;

import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.delivery.application.common.bo.logistics.LogisticsSkuCreateBO;
import com.ets.delivery.application.common.dto.logistics.LogisticsAcceptDTO;
import com.ets.delivery.application.infra.entity.LogisticsSku;
import com.ets.delivery.application.infra.mapper.LogisticsSkuMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 发货单商品列表 业务处理类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-05-11
 */
@Service
@Slf4j
public class LogisticsSkuService extends BaseService<LogisticsSkuMapper, LogisticsSku> {
    /*
     * 创建发货单
     */
    public  boolean create(String logisticsSn,LogisticsAcceptDTO logisticsAcceptDTO){
        //生成商品发货数据
        logisticsAcceptDTO.getSkuList().forEach(sku -> {
            LogisticsSku logisticsSku = new LogisticsSku();
            logisticsSku.setLogisticsSn(logisticsSn);
            logisticsSku.setSku(sku.getSku());
            logisticsSku.setGoodsName(sku.getGoodsName());
            logisticsSku.setStorageSku(sku.getStorageSku());
            logisticsSku.setNums(sku.getNums());
            logisticsSku.setCreatedAt(LocalDateTime.now());
            logisticsSku.setUpdatedAt(LocalDateTime.now());
            this.baseMapper.insert(logisticsSku);
        });
        return true;
    }

    public  boolean create(String logisticsSn, List<LogisticsSkuCreateBO> skuList){
        skuList.forEach(sku -> {
            LogisticsSku logisticsSku = new LogisticsSku();
            logisticsSku.setLogisticsSn(logisticsSn);
            logisticsSku.setSku(sku.getSku());
            logisticsSku.setGoodsName(sku.getGoodsName());
            logisticsSku.setStorageSku(sku.getStorageSku());
            logisticsSku.setNums(sku.getNums());
            logisticsSku.setCreatedAt(LocalDateTime.now());
            logisticsSku.setUpdatedAt(LocalDateTime.now());
            this.baseMapper.insert(logisticsSku);
        });
        return true;
    }

    public void delete(String logisticsSn) {
        Wrapper<LogisticsSku> wrapper = Wrappers.<LogisticsSku>lambdaQuery()
                .eq(LogisticsSku::getLogisticsSn, logisticsSn);
        this.baseMapper.delete(wrapper);
    }

    /*
     * 获取发货单商品列表
     */
    public List<LogisticsSku> getListByLogisticsSn(String logisticsSn){
        // 查询条件设置
        LambdaQueryWrapper<LogisticsSku> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LogisticsSku::getLogisticsSn, logisticsSn);
        return this.baseMapper.selectList(wrapper);

    }

    public IPage<LogisticsSku> getPageBySku(String sku, Integer pageNum, Integer pageSize) {
        Wrapper<LogisticsSku> wrapper = Wrappers.<LogisticsSku>lambdaQuery()
                .eq(LogisticsSku::getSku, sku)
                .orderByDesc(LogisticsSku::getCreatedAt);
        return this.baseMapper.selectPage(new Page<>(pageNum, pageSize), wrapper);
    }

    public List<LogisticsSku> getLogisticsSumByStorageSkuList(List<String> storageSkuList, List<String> logisticsSnList) {
        QueryWrapper<LogisticsSku> wrapper = new QueryWrapper<>();
        wrapper.select("storage_sku, sum(nums) as sumGoodsNums")
                .lambda()
                .in(LogisticsSku::getLogisticsSn, logisticsSnList)
                .in(LogisticsSku::getStorageSku, storageSkuList)
                .groupBy(LogisticsSku::getStorageSku);
        return this.baseMapper.selectList(wrapper);
    }

    public List<LogisticsSku> getListByLogisticsSns(List<String> logisticsSns) {
        Wrapper<LogisticsSku> wrapper = Wrappers.<LogisticsSku>lambdaQuery()
                .in(LogisticsSku::getLogisticsSn, logisticsSns);
        return this.baseMapper.selectList(wrapper);
    }

    public LogisticsSku getByStorageSku(String logisticsSn, String storageSku) {
        LambdaQueryWrapper<LogisticsSku> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(LogisticsSku::getLogisticsSn, logisticsSn)
                .eq(LogisticsSku::getStorageSku, storageSku);
        return getOneByWrapper(wrapper);
    }
}
