package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ets.delivery.application.infra.entity.StorageRecordSerial;
import com.ets.delivery.application.infra.mapper.StorageRecordSerialMapper;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
@Service
@DS("db-delivery")
public class StorageRecordSerialService extends BaseService<StorageRecordSerialMapper, StorageRecordSerial> {

    /**
     * 根据记录单号获取序列号列表
     */
    public List<String> getSerialListByRecordSn(String recordSn) {
        LambdaQueryWrapper<StorageRecordSerial> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StorageRecordSerial::getRecordSn, recordSn);
        List<StorageRecordSerial> serialRecords = this.list(wrapper);
        return serialRecords.stream()
                .map(StorageRecordSerial::getSerialNo)
                .collect(Collectors.toList());
    }

    /**
     * 根据记录单号删除序列号记录
     */
    public boolean deleteByRecordSn(String recordSn) {
        LambdaQueryWrapper<StorageRecordSerial> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StorageRecordSerial::getRecordSn, recordSn);
        return this.remove(wrapper);
    }
}
