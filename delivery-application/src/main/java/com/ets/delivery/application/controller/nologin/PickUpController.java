package com.ets.delivery.application.controller.nologin;

import com.alibaba.fastjson.JSON;
import com.ets.common.BeanHelper;
import com.ets.common.JsonResult;
import com.ets.delivery.application.app.business.PickUpBusiness;
import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import com.ets.delivery.application.infra.entity.Pickup;
import com.ets.delivery.application.infra.entity.StorageSkuMap;
import com.ets.delivery.application.infra.service.PickupService;
import com.ets.delivery.application.infra.service.StorageSkuMapService;
import com.ets.delivery.feign.request.pickup.*;
import com.ets.delivery.feign.response.pickup.PickUpBookingInfoVO;
import com.ets.delivery.feign.response.pickup.PickUpCreateVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/no-login/pickUp")
public class PickUpController {

    @Autowired
    private PickUpBusiness pickUpBusiness;

    @Autowired
    StorageSkuMapService storageSkuMapService;

    @Autowired
    private PickupService pickupService;

    @RequestMapping("/getBookingInfo")
    public JsonResult<PickUpBookingInfoVO> getBookingInfo(@RequestBody @Valid PickUpBookingInfoDTO bookingInfoDTO) {
        PickUpBookingInfoVO bookingInfo = pickUpBusiness.getBookingInfo(bookingInfoDTO);
        return JsonResult.ok(bookingInfo);
    }

    @RequestMapping("/create")
    public JsonResult<PickUpCreateVO> create(@RequestBody @Valid PickUpCreateDTO createDTO) {
        if (StringUtils.isEmpty(createDTO.getStorageCode())) {
            createDTO.setStorageCode(StorageCodeEnum.YUNDA.getValue());
        }
        PickUpCreateVO pickUpCreateVO = pickUpBusiness.createPickUpOrder(createDTO, "");
        return JsonResult.ok(pickUpCreateVO);
    }

    @RequestMapping("/cancel")
    public JsonResult<?> cancel(@RequestBody @Valid PickUpCancelDTO cancelDTO) {
        pickUpBusiness.cancelPickUpOrder(cancelDTO);
        return JsonResult.ok();
    }

    @RequestMapping("/createByGoods")
    public JsonResult<PickUpCreateVO> createByGoods(@RequestBody @Valid PickUpCreateByGoodsDTO dto) {
        // 默认韵达仓
        if (StringUtils.isEmpty(dto.getStorageCode())) {
            dto.setStorageCode(StorageCodeEnum.YUNDA.getValue());
        }

        PickUpCreateDTO createDTO = BeanHelper.copy(PickUpCreateDTO.class, dto);
        // 目前仅支持一个商品, 仓库写死为韵达
        StorageSkuMap storageSku = storageSkuMapService.getBySku(dto.getStorageCode(), dto.getSkuInfo().get(0).getSkuSn());

        List<PickupGoodsBO> goodsList = new ArrayList<>();
        PickupGoodsBO bo = new PickupGoodsBO();
        bo.setGoodsCode(storageSku.getStorageSku());
        bo.setQuantity(dto.getSkuInfo().get(0).getCount());
        bo.setGoodsName(storageSku.getGoodsName());
        goodsList.add(bo);

        createDTO.setGoodsList(goodsList);

        PickUpCreateVO pickUpCreateVO = pickUpBusiness.createPickUpOrder(createDTO, JSON.toJSONString(dto.getSkuInfo()));
        return JsonResult.ok(pickUpCreateVO);
    }

    @RequestMapping("/getPickup")
    public JsonResult<Pickup> getPickup(@RequestParam(name = "pickupSn") String pickupSn) {

        return JsonResult.ok(pickupService.getOneByPickUpSn(pickupSn));
    }

    @RequestMapping("/getPickupByOrderSn")
    public JsonResult<Pickup> getPickupByOrderSn(@RequestParam(name = "orderSn") String orderSn) {

        Pickup pickup = pickupService.getOneByOrderSn(orderSn);

        return JsonResult.ok(pickup);
    }

}