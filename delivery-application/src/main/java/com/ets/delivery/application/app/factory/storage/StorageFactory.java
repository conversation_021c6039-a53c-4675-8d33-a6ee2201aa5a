package com.ets.delivery.application.app.factory.storage;

import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.factory.storage.impl.StorageBase;
import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;

import cn.hutool.extra.spring.SpringUtil;

public class StorageFactory {

    public static StorageBase create(String storageCode) {

        StorageCodeEnum storageCodeEnum = StorageCodeEnum.getByType(storageCode);
        if (storageCodeEnum == null) {
            ToolsHelper.throwException("暂不支持此物流公司");
        }

        return SpringUtil.getBean(storageCodeEnum.getJob());
    }
}
