package com.ets.delivery.application.common.vo.serial;

import com.ets.delivery.application.common.consts.yunda.YundaInventoryTypeEnum;
import com.ets.delivery.application.common.consts.serial.SerialStatusEnum;
import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class AdminSerialListVO {

    private Integer id;

    /**
     * 序列号
     */
    private String serialNo;

    /**
     * 仓库编码
     */
    private String storageCode;

    /**
     * 仓库编码描述
     */
    private String storageCodeStr;

    /**
     * 仓库SKU
     */
    private String storageSku;

    /**
     * 库存类型[字符串类型]
     */
    private String inventoryType;

    /**
     * 库存类型描述
     */
    private String inventoryTypeStr;

    /**
     * 状态[1-在库 2-出库 3-发货 4-退回]
     */
    private Integer status;

    /**
     * 状态描述
     */
    private String statusStr;

    /**
     * 最新业务来源
     */
    private String latestBusinessSource;

    /**
     * 最新业务单号
     */
    private String latestBusinessSn;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime updatedAt;

    public String getStorageCodeStr() {
        return StorageCodeEnum.map.getOrDefault(storageCode, "-");
    }

    public String getInventoryTypeStr() {
        return YundaInventoryTypeEnum.getDescByValue(inventoryType);
    }

    public String getStatusStr() {
        return SerialStatusEnum.getDescByValue(status);
    }
}