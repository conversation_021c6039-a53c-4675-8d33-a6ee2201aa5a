package com.ets.delivery.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.common.JsonResult;
import com.ets.delivery.application.app.business.serial.SerialBusiness;
import com.ets.delivery.application.common.dto.serial.AdminSerialListDTO;
import com.ets.delivery.application.common.dto.serial.AdminSerialLogListDTO;
import com.ets.delivery.application.common.dto.serial.AdminStockSerialListDTO;
import com.ets.delivery.application.common.vo.serial.AdminSerialListVO;
import com.ets.delivery.application.common.vo.serial.AdminSerialLogListVO;
import com.ets.delivery.application.common.vo.serial.AdminSerialSelectOptionsVO;
import com.ets.delivery.application.common.vo.serial.AdminStockSerialListVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * <p>
 * 序列号管理 控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
@RestController
@RequestMapping("/admin/serial")
public class AdminSerialController {

    @Autowired
    private SerialBusiness serialBusiness;

    /**
     * 序列号列表查询接口
     * 支持按序列号、仓库编码、SKU、状态等条件分页查询
     */
    @RequestMapping("/get-list")
    public JsonResult<IPage<AdminSerialListVO>> getList(@RequestBody @Valid AdminSerialListDTO listDTO) {
        return JsonResult.ok(serialBusiness.getList(listDTO));
    }

    /**
     * 序列号日志查询接口
     * 根据序列号查询该序列号的所有操作日志记录
     */
    @RequestMapping("/get-log-list")
    public JsonResult<IPage<AdminSerialLogListVO>> getLogList(@RequestBody @Valid AdminSerialLogListDTO listDTO) {
        return JsonResult.ok(serialBusiness.getLogList(listDTO));
    }

    /**
     * 获取序列号相关枚举选项
     * 用于前端下拉筛选组件
     */
    @RequestMapping("/get-select-options")
    public JsonResult<AdminSerialSelectOptionsVO> getSelectOptions() {
        return JsonResult.ok(serialBusiness.getSelectOptions());
    }

    /**
     * 出入库序列号查询接口
     * 返回分页格式，只包含id和serialNo字段
     */
    @RequestMapping("/get-stock-serial-list")
    public JsonResult<IPage<AdminStockSerialListVO>> getStockSerialList(@RequestBody @Valid AdminStockSerialListDTO listDTO) {
        return JsonResult.ok(serialBusiness.getStockSerialList(listDTO));
    }

}