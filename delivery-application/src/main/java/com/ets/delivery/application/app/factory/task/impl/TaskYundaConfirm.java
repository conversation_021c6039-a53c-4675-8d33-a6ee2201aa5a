package com.ets.delivery.application.app.factory.task.impl;

import com.alibaba.fastjson.JSON;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.factory.task.TaskFactory;
import com.ets.delivery.application.app.business.serial.SerialBusiness;
import com.ets.delivery.application.common.consts.serial.SerialTypeEnum;
import com.ets.delivery.application.app.thirdservice.business.WorkWeChatBusiness;
import com.ets.delivery.application.common.bo.task.TaskLogisticsConfirmYundaBO;
import com.ets.delivery.application.common.bo.serial.SerialProcessBO;
import com.ets.delivery.application.common.config.WeChatRobotConfig;
import com.ets.delivery.application.common.consts.logistics.*;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordErrorCodeConstant;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.delivery.application.common.consts.yunda.YundaDeliveryOrderStatusEnum;
import com.ets.delivery.application.common.dto.task.TaskRecordDTO;
import com.ets.delivery.application.infra.entity.ExWarehouse;
import com.ets.delivery.application.infra.entity.Logistics;
import com.ets.delivery.application.infra.entity.TaskRecord;
import com.ets.delivery.application.infra.service.ExWarehouseService;
import com.ets.delivery.application.infra.service.LogisticsLogService;
import com.ets.delivery.application.infra.service.LogisticsService;
import com.ets.delivery.application.infra.service.TaskRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@Component
public class TaskYundaConfirm extends TaskBase {

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private LogisticsLogService logisticsLogService;

    @Autowired
    private ExWarehouseService exWarehouseService;

    @Autowired
    private TaskRecordService taskRecordService;

    @Autowired
    private WorkWeChatBusiness workWeChatBusiness;

    @Autowired
    private WeChatRobotConfig robotConfig;

    @Autowired
    private SerialBusiness serialBusiness;

    @Override
    public void childExec(TaskRecord taskRecord) {
        TaskLogisticsConfirmYundaBO confirmYundaBO = JSON.parseObject(taskRecord.getNotifyContent(), TaskLogisticsConfirmYundaBO.class);

        // 查询发货单
        Logistics logistics = logisticsService.getByLogisticsSn(taskRecord.getReferSn());
        if (ObjectUtils.isEmpty(logistics) || logistics.getStatus().equals(LogisticsStatusEnum.STATUS_CANCEL.getValue())) {
            String markdown = String.format(
                    "【发货确认】<font color=\"warning\">发货单异常</font>\n " +
                            "><font color=\"info\">发货单号</font>：%s\n" +
                            "><font color=\"info\">快递单号</font>：%s\n",
                    logistics.getLogisticsSn(),
                    confirmYundaBO.getExpressNumber()
            );
            workWeChatBusiness.sendMarkdown(markdown, robotConfig.getLogisticsAlarmKey());
            ToolsHelper.throwException("发货单不存在或已取消", TaskRecordErrorCodeConstant.ERROR_CODE_NEED_FINISH);
        }

        // 查询出库单
        ExWarehouse exWarehouse = exWarehouseService.getOneByIsvUuid(taskRecord.getReferSn());
        if (ObjectUtils.isEmpty(exWarehouse) || StringUtils.isEmpty(exWarehouse.getDeliverOrderNo())) {
            String msg = "发货单" + taskRecord.getReferSn() + "出库单数据异常";

            // 记录日志
            logisticsLogService.addLog(logistics.getId(),
                    LogisticsOperateTypeEnum.TYPE_DELIVERY.getCode(),
                    msg,
                    "system");

            ToolsHelper.throwException(msg);
        }

        // 更新出库单
        if (StringUtils.isEmpty(exWarehouse.getWayBill())) {
            exWarehouse.setWayBill(confirmYundaBO.getExpressNumber());
            exWarehouse.setShipperNo(StringUtils.isNotEmpty(confirmYundaBO.getExpressCorpNo()) ? confirmYundaBO.getExpressCorpNo().toLowerCase() : confirmYundaBO.getExpressCorp());
            exWarehouse.setShipperName(confirmYundaBO.getExpressCorp());
            exWarehouse.setWarehouseNo(confirmYundaBO.getWarehouseNo());
            exWarehouse.setCurrentStatus(confirmYundaBO.getDeliveryStatus().equals(YundaDeliveryOrderStatusEnum.DELIVERED.getValue()) ? 10020 : 0);
            exWarehouseService.updateById(exWarehouse);
        }

        // 更新发货单
        if (StringUtils.isEmpty(logistics.getExpressNumber())) {
            logistics.setExpressCorp(confirmYundaBO.getExpressCorp());
            logistics.setExpressNumber(confirmYundaBO.getExpressNumber());
            logistics.setDeliveryStatus(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_SHIPPED.getValue());
            logistics.setDeliveryTime(ObjectUtils.isNotEmpty(confirmYundaBO.getDeliveryTime()) ?
                    LocalDateTime.parse(confirmYundaBO.getDeliveryTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) :
                    LocalDateTime.now());
            logistics.setDeliveryRemark("韵达发货成功");
            logistics.setNotifyStatus(LogisticsNotifyStatusEnum.NOTIFY_STATUS_PROCESSING.getValue());
            logistics.setNotifyTime(null);
            logistics.setStatus(LogisticsStatusEnum.STATUS_NORMAL.getValue());
            logisticsService.updateById(logistics);

            // 记录日志
            logisticsLogService.addLog(logistics.getId(),
                    LogisticsOperateTypeEnum.TYPE_DELIVERY.getCode(),
                    "韵达物流单号" + confirmYundaBO.getExpressNumber(),
                    "system");

            // 创建发货通知任务
            Map<String, Object> contentMap = new HashMap<>();
            contentMap.put("logistics_id", logistics.getId());
            String content = JSON.toJSONString(contentMap);

            TaskRecord task = taskRecordService.getOneByCondition(logistics.getLogisticsSn(),
                    TaskRecordReferTypeEnum.TASK_LOGISTICS_SHIP.getType(),
                    content
            );

            if (ObjectUtils.isEmpty(task)) {
                TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
                taskRecordDTO.setReferType(TaskRecordReferTypeEnum.TASK_LOGISTICS_SHIP.getType());
                taskRecordDTO.setReferSn(logistics.getLogisticsSn());
                taskRecordDTO.setNotifyContent(content);

                TaskFactory.create(TaskRecordReferTypeEnum.TASK_LOGISTICS_SHIP).addAndPush(taskRecordDTO);
            }
        }

        // 处理发货序列号状态更新（按SKU分组处理）
        if (confirmYundaBO.getSkuSerialInfoMap() != null && !confirmYundaBO.getSkuSerialInfoMap().isEmpty()) {
            for (Map.Entry<String, TaskLogisticsConfirmYundaBO.SkuSerialInfo> entry : confirmYundaBO.getSkuSerialInfoMap().entrySet()) {
                String itemCode = entry.getKey();
                TaskLogisticsConfirmYundaBO.SkuSerialInfo skuSerialInfo = entry.getValue();

                if (skuSerialInfo != null && skuSerialInfo.getSerialList() != null && !skuSerialInfo.getSerialList().isEmpty()) {
                    try {
                        SerialProcessBO processBO = SerialProcessBO.builder()
                                .serialList(skuSerialInfo.getSerialList())
                                .businessSn(confirmYundaBO.getLogisticsSn())
                                .storageSku(itemCode)
                                .inventoryType(skuSerialInfo.getInventoryType())
                                .operator("admin")
                                .remark("韵达发货确认")
                                .storageCode(logistics.getStorageCode())
                                .businessSource(LogisticsOrderTypeEnum.map.getOrDefault(logistics.getOrderType(), ""))
                                .build();
                        serialBusiness.processSerials(SerialTypeEnum.DELIVERY, processBO);
                        log.info("处理发货序列号成功，发货单：{}，SKU：{}，序列号数量：{}",
                                confirmYundaBO.getLogisticsSn(), itemCode, skuSerialInfo.getSerialList().size());
                    } catch (Exception e) {
                        log.error("处理发货序列号失败，发货单：{}，SKU：{}，序列号：{}，错误：{}",
                                confirmYundaBO.getLogisticsSn(), itemCode, skuSerialInfo.getSerialList(), e.getMessage());
                    }
                }
            }
        }
    }
}
