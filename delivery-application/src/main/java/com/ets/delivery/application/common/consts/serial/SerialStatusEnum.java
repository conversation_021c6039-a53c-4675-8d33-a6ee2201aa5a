package com.ets.delivery.application.common.consts.serial;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 序列号状态枚举
 */
@Getter
@AllArgsConstructor
public enum SerialStatusEnum {
    
    /**
     * 在库
     */
    IN_STOCK(1, "在库"),
    
    /**
     * 出库
     */
    OUT_STOCK(2, "出库"),
    
    /**
     * 退回
     */
    RETURNED(3, "退回");

    private final Integer value;
    private final String desc;

    public static final Map<Integer, String> map;
    static {
        map = Arrays.stream(SerialStatusEnum.values()).collect(Collectors.toMap(SerialStatusEnum::getValue, SerialStatusEnum::getDesc));
    }

    public static boolean isValidValue(Integer value) {
        if (value == null) {
            return false;
        }
        return Arrays.stream(SerialStatusEnum.values())
                .anyMatch(enumValue -> enumValue.getValue().equals(value));
    }

    public static List<Map<String, String>> getSelectOptions() {
        return Arrays.stream(SerialStatusEnum.values())
                .map(enumValue -> Map.of("label", enumValue.getDesc(), "value", enumValue.getValue().toString()))
                .collect(Collectors.toList());
    }

    public static String getDescByValue(Integer value) {
        return map.getOrDefault(value, "未知");
    }
}