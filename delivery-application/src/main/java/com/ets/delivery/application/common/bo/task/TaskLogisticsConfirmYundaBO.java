package com.ets.delivery.application.common.bo.task;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

@Data
public class TaskLogisticsConfirmYundaBO {

    /**
     * 发货单号
     */
    public String logisticsSn;

    /**
     * 仓库编号
     */
    public String warehouseNo;

    /**
     * 物流公司编码
     */
    public String expressCorpNo;

    /**
     * 物流公司
     */
    public String expressCorp;

    /**
     * 物流单号
     */
    public String expressNumber;

    /**
     * 出库单状态
     */
    public String deliveryStatus;

    /**
     * 发货时间
     */
    public String deliveryTime;

    /**
     * 按SKU分组的序列号信息
     * key: itemCode (SKU编码)
     * value: SkuSerialInfo (包含序列号列表和库存类型)
     */
    public Map<String, SkuSerialInfo> skuSerialInfoMap;

    /**
     * SKU序列号信息内部类
     * 用于封装每个SKU的序列号列表和库存类型
     */
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SkuSerialInfo {
        
        /**
         * 序列号列表
         */
        private List<String> serialList;
        
        /**
         * 库存类型（ZP/CC/JS/XS等）
         */
        private String inventoryType;
    }
}
