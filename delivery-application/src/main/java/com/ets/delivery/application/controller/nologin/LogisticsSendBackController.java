package com.ets.delivery.application.controller.nologin;


import com.alibaba.fastjson.JSON;
import com.ets.common.BeanHelper;
import com.ets.common.JsonResult;
import com.ets.delivery.application.app.business.SendBackBusiness;
import com.ets.delivery.application.common.bo.sendback.LogisticsSendBackCreateBO;
import com.ets.delivery.feign.request.express.SendBackUpdateExpressDTO;
import com.ets.delivery.application.infra.entity.StorageSkuMap;
import com.ets.delivery.application.infra.service.StorageSkuMapService;
import com.ets.delivery.feign.request.logistics.SendBackCreateByGoodsDTO;
import com.ets.delivery.application.common.vo.SendBackInfoVO;
import com.ets.delivery.application.infra.entity.LogisticsSendBack;
import com.ets.delivery.feign.response.logistics.LogisticsSendBackVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

/**
 * <p>
 * 寄回件列表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-29
 */
@RestController
@RequestMapping("/no-login/logisticsSendBack")
@Slf4j
public class LogisticsSendBackController {

    @Autowired
    SendBackBusiness sendBackBusiness;

    @Autowired
    private StorageSkuMapService storageSkuMapService;

    @RequestMapping("/getOrderInfo")
    public JsonResult<SendBackInfoVO> getOrderInfo(@RequestParam(value = "logisticsSn") String logisticsSn) {
        SendBackInfoVO sendBackInfo = sendBackBusiness.getOrderInfoByLogisticsSn(logisticsSn);
        return JsonResult.ok(sendBackInfo);
    }

    @RequestMapping("/createByGoods")
    public JsonResult<LogisticsSendBackVO> createByGoods(@RequestBody @Valid SendBackCreateByGoodsDTO dto) {

        LogisticsSendBackCreateBO bo = BeanHelper.copy(LogisticsSendBackCreateBO.class, dto);
        bo.setGoodsSkuInfo(JSON.toJSONString(dto.getSkuInfo()));
        bo.setReviceName(dto.getReceiveName());
        bo.setReviceArea(dto.getReceiveArea());
        bo.setReviceAddress(dto.getReceiveAddress());
        bo.setRevicePhone(dto.getReceivePhone());
        bo.setSendPhone(dto.getSendPhone());

        // 目前仅支持一个商品
        StorageSkuMap storageSku = storageSkuMapService.getBySku(dto.getStorageCode(), dto.getSkuInfo().get(0).getSkuSn());
        bo.setGoodsCode(storageSku.getStorageSku());
        bo.setNums(dto.getSkuInfo().get(0).getCount());

        LogisticsSendBack sendBack = sendBackBusiness.create(bo);

        LogisticsSendBackVO vo = new LogisticsSendBackVO();
        vo.setSendbackSn(sendBack.getSendbackSn());

        return JsonResult.ok(vo);
    }

    @RequestMapping("/updateExpress")
    public JsonResult<Object> updateExpress(@RequestBody @Valid SendBackUpdateExpressDTO dto) {

        sendBackBusiness.updateExpress(dto);

        return JsonResult.ok();
    }

    @RequestMapping("/cancelSendBack")
    public JsonResult<Object> cancelSendBack(@RequestParam(value = "orderSn") String orderSn) {

        sendBackBusiness.cancelSendBack(orderSn);

        return JsonResult.ok();
    }

    @RequestMapping("/subscribeBySendBack")
    public JsonResult<Object> subscribeBySendBack(@RequestParam(value = "sendBackSn") String sendBackSn) {

        sendBackBusiness.subscribeBySendBack(sendBackSn);

        return JsonResult.ok();
    }

}

