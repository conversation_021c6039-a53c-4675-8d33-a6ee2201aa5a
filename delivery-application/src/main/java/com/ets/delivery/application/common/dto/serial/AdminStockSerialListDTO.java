package com.ets.delivery.application.common.dto.serial;

import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;

@Data
public class AdminStockSerialListDTO {

    /**
     * 出入库关联单号
     */
    private String stockSn;

    /**
     * 出入库商品记录id
     */
    private Integer stockGoodsId;

    /**
     * 页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 分页大小
     */
    @Max(value = 100, message = "每页最多100条")
    private Integer pageSize = 20;
}