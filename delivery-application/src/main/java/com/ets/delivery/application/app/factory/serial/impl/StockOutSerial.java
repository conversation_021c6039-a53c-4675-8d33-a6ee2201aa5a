package com.ets.delivery.application.app.factory.serial.impl;

import com.ets.delivery.application.common.bo.serial.SerialProcessBO;
import com.ets.delivery.application.common.consts.serial.SerialStatusEnum;
import com.ets.delivery.application.infra.entity.StockGoodsInfo;
import com.ets.delivery.application.infra.entity.StockGoodsSerial;
import com.ets.delivery.application.infra.service.StockGoodsInfoService;
import com.ets.delivery.application.infra.service.StockGoodsSerialService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 出库序列号处理器
 */
@Slf4j
@Component
public class StockOutSerial extends SerialBase {
    
    @Autowired
    private StockGoodsInfoService stockGoodsInfoService;
    
    @Autowired
    private StockGoodsSerialService stockGoodsSerialService;
    

    
    @Override
    protected void setTargetStatus(SerialProcessBO processBO) {
        processBO.setTargetStatus(SerialStatusEnum.OUT_STOCK);
    }
    
    @Override
    protected void executeSpecificLogic(SerialProcessResult result, SerialProcessBO processBO) {
        // 处理不存在序列号的预警
        handleMissingSerialWarning(result.getNewSerials(), processBO);

        updateStockGoodsSerial(processBO.getSerialList(), processBO.getBusinessSn(), processBO.getStorageSku());
    }
    
    /**
     * 更新StockGoodsSerial表数据
     */
    private void updateStockGoodsSerial(List<String> serialList, String businessSn, String storageSku) {
        try {
            // 查询商品信息
            StockGoodsInfo goodsInfo = stockGoodsInfoService.getByGoodsCode(businessSn, storageSku);
            if (goodsInfo == null) {
                log.warn("未找到商品信息: storageSku={}", storageSku);
                return;
            }
            
            // 批量创建StockGoodsSerial记录
            List<StockGoodsSerial> stockGoodsSerials = serialList.stream()
                    .map(serialNo -> {
                        StockGoodsSerial stockGoodsSerial = new StockGoodsSerial();
                        stockGoodsSerial.setSerialNo(serialNo);
                        stockGoodsSerial.setStockSn(businessSn);
                        stockGoodsSerial.setStockGoodsId(goodsInfo.getId());
                        stockGoodsSerial.setCreatedAt(LocalDateTime.now());
                        stockGoodsSerial.setUpdatedAt(LocalDateTime.now());
                        return stockGoodsSerial;
                    })
                    .collect(Collectors.toList());
            
            if (!stockGoodsSerials.isEmpty()) {
                stockGoodsSerialService.saveBatch(stockGoodsSerials);
            }
        } catch (Exception e) {
            log.error("更新StockGoodsSerial表数据失败: businessSn={}, storageSku={}", businessSn, storageSku, e);
        }
    }
}