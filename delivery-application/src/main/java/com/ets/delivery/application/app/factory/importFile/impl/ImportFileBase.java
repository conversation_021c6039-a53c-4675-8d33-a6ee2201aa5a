package com.ets.delivery.application.app.factory.importFile.impl;


import com.ets.common.ToolsHelper;
import com.ets.common.util.ThreadLocalUtil;
import com.ets.delivery.application.app.factory.importFile.IImportFile;
import com.ets.delivery.application.common.consts.ThreadLocalCacheKey;
import com.ets.delivery.application.infra.entity.ImportFileRecord;
import com.ets.delivery.application.infra.entity.User;
import com.ets.delivery.application.infra.service.ImportFileRecordService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.multipart.MultipartFile;

import jakarta.annotation.Resource;
import java.time.LocalDateTime;


public abstract class ImportFileBase<T> implements IImportFile<T> {

    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    @Value("${spring.profiles.active}")
    private String ACTIVE;

    @Autowired
    private ImportFileRecordService importFileRecordService;

    @Override
    public void importFileCheck(MultipartFile file) {
        if (file.isEmpty()) {
            ToolsHelper.throwException("文件不存在");
        }

        // 格式校验
        String filename = file.getOriginalFilename();
        if (StringUtils.isEmpty(filename)) {
            ToolsHelper.throwException("文件名为空");
        }
        if (!filename.endsWith(".xls") && !filename.endsWith(".xlsx")) {
            ToolsHelper.throwException("系统不支持该文件格式，请上传.xls  .xlsx格式的文件");
        }
    }

    @Override
    public ImportFileRecord initImportRecord(String filename, String importType) {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);

        String batchNo = ToolsHelper.genNum(redisPermanentTemplate, importType, ACTIVE, 8);
        ImportFileRecord importFileRecord = new ImportFileRecord();
        importFileRecord.setImportType(importType);
        importFileRecord.setBatchNo(batchNo);
        importFileRecord.setFilename(filename);
        importFileRecord.setOperator(user.getUsername());
        importFileRecord.setCreatedAt(LocalDateTime.now());
        importFileRecord.setUpdatedAt(LocalDateTime.now());
        importFileRecordService.save(importFileRecord);
        return importFileRecord;
    }
}
