package com.ets.delivery.application.controller.nologin;

import com.ets.delivery.application.app.business.aftersalesreviews.AfterSalesReviewsBusiness;
import com.ets.delivery.application.common.dto.aftersalesreviews.CancelAfterSalesReviewDTO;
import com.ets.delivery.application.common.dto.aftersalesreviews.CreateAfterSalesReviewDTO;
import com.ets.delivery.application.common.vo.aftersalesreivews.AfterSalesReviewsVO;
import com.ets.common.JsonResult;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 售后审核单
 */
@Slf4j
@RestController
@RequestMapping("/aftersales-reviews")
public class AfterSalesReviewsController {

    @Autowired
    private AfterSalesReviewsBusiness afterSalesReviewsBusiness;

    /**
     * 创建售后审核单
     *
     * @param dto 创建售后审核单请求参数
     * @return 售后审核单
     */
    @PostMapping("/create")
    public JsonResult<AfterSalesReviewsVO> createAfterSalesReview(@RequestBody @Valid CreateAfterSalesReviewDTO dto) {
        AfterSalesReviewsVO reviewVO = afterSalesReviewsBusiness.createAfterSalesReview(dto);
        return JsonResult.ok(reviewVO);
    }

    /**
     * 取消售后审核单
     *
     * @param dto 取消售后审核单请求参数
     */
    @PostMapping("/cancel")
    public JsonResult<Void> cancelAfterSalesReview(@RequestBody @Valid CancelAfterSalesReviewDTO dto) {
        afterSalesReviewsBusiness.cancelAfterSalesReview(dto);
        return JsonResult.ok();
    }
}
