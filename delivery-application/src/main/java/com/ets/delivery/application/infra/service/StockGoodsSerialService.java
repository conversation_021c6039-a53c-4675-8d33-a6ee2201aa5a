package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.delivery.application.common.dto.serial.AdminStockSerialListDTO;
import com.ets.delivery.application.infra.entity.StockGoodsSerial;
import com.ets.delivery.application.infra.mapper.StockGoodsSerialMapper;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
@Service
@DS("db-delivery")
public class StockGoodsSerialService extends BaseService<StockGoodsSerialMapper, StockGoodsSerial> {

    /**
     * 分页查询出入库序列号列表
     */
    public IPage<StockGoodsSerial> getPage(AdminStockSerialListDTO listDTO) {
        Wrapper<StockGoodsSerial> wrapper = Wrappers.<StockGoodsSerial>lambdaQuery()
                .eq(StringUtils.isNotEmpty(listDTO.getStockSn()), StockGoodsSerial::getStockSn, listDTO.getStockSn())
                .eq(ObjectUtils.isNotEmpty(listDTO.getStockGoodsId()), StockGoodsSerial::getStockGoodsId, listDTO.getStockGoodsId())
                .orderByDesc(StockGoodsSerial::getCreatedAt);
        
        Page<StockGoodsSerial> page = new Page<>(listDTO.getPageNum(), listDTO.getPageSize());
        return this.page(page, wrapper);
    }
}
