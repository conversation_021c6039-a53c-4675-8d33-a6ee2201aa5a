package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 序列号记录表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
@Data
@TableName("etc_serial")
public class Serial extends BaseEntity<Serial> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 序列号
     */
    private String serialNo;

    /**
     * 仓库编码
     */
    private String storageCode;

    /**
     * 仓库sku
     */
    private String storageSku;

    /**
     * 库存类型
     */
    private String inventoryType;

    /**
     * 状态[1-在库 2-出库 3-退回]
     */
    private Integer status;

    /**
     * 最新业务来源
     */
    private String latestBusinessSource;

    /**
     * 最新业务单号
     */
    private String latestBusinessSn;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
