package com.ets.delivery.application.controller.notify;

import com.ets.delivery.application.app.business.MaoChaoBusiness;
import com.ets.delivery.application.common.dto.maoChao.MaoChaoDeliveryNotifyDTO;
import com.ets.delivery.application.common.vo.maoChao.MaoChaoResultVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.servlet.http.HttpServletRequest;

@Slf4j
@RestController
@RequestMapping("/notify/mao<PERSON>hao")
public class MaoChaoController {

    @Autowired
    private MaoChaoBusiness maoChaoBusiness;

    @RequestMapping("/deliveryOrderConfirm")
    public MaoChaoResultVO deliveryOrderConfirm(@RequestBody MaoChaoDeliveryNotifyDTO dto, HttpServletRequest request) {

        return maoChaoBusiness.deliveryOrderConfirm(dto, request);
    }
}
