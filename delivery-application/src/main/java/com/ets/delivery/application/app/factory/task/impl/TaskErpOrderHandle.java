package com.ets.delivery.application.app.factory.task.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.business.ErpOrderBusiness;
import com.ets.delivery.application.app.business.ExpressBusiness;
import com.ets.delivery.application.app.business.LogisticsBusiness;
import com.ets.delivery.application.app.business.ProductOrderBusiness;
import com.ets.delivery.application.common.bo.erp.ErpLogisticsSkuBO;
import com.ets.delivery.application.common.bo.logistics.LogisticsErpOrderCreateBO;
import com.ets.delivery.application.common.bo.productOrder.CreateOrderFromExternalBO;
import com.ets.delivery.application.common.bo.task.TaskLogisticsConfirmYundaBO;
import com.ets.delivery.application.common.consts.erpRecord.ErpRecordOrderSourceEnum;
import com.ets.delivery.application.common.consts.erpRecord.ErpRecordOrderTypeEnum;
import com.ets.delivery.application.common.consts.erpRecord.ErpRecordStatusEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsOrderTypeEnum;
import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import com.ets.delivery.application.common.dto.express.ExpressSubscribeDTO;
import com.ets.delivery.application.common.vo.productOrder.ProductOrderExternalCreateVO;
import com.ets.delivery.application.infra.entity.ErpOrder;
import com.ets.delivery.application.infra.entity.ErpRecord;
import com.ets.delivery.application.infra.entity.Logistics;
import com.ets.delivery.application.infra.entity.TaskRecord;
import com.ets.delivery.application.infra.service.ErpOrderService;
import com.ets.delivery.application.infra.service.ErpRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
@Slf4j
public class TaskErpOrderHandle extends TaskBase {

    @Autowired
    private ErpRecordService erpRecordService;

    @Autowired
    private ErpOrderService erpOrderService;

    @Autowired
    private ProductOrderBusiness productOrderBusiness;

    @Autowired
    private LogisticsBusiness logisticsBusiness;

    @Autowired
    private ExpressBusiness expressBusiness;

    @Autowired
    private ErpOrderBusiness erpOrderBusiness;

    @Override
    public void childExec(TaskRecord taskRecord) {
        TaskLogisticsConfirmYundaBO confirmYundaBO = JSON.parseObject(taskRecord.getNotifyContent(), TaskLogisticsConfirmYundaBO.class);

        // 查询erp记录
        ErpRecord record = erpRecordService.getOneByColumn(taskRecord.getReferSn(), ErpRecord::getErpSn);
        if (ObjectUtils.isEmpty(record)) {
            return;
        }
        if (record.getStatus().equals(ErpRecordStatusEnum.SUCCESS.getValue())) {
            return;
        }

        List<ErpLogisticsSkuBO> erpSkuList = JSON.parseArray(record.getLogisticsSku(), ErpLogisticsSkuBO.class);

        List<LogisticsErpOrderCreateBO.Sku> createSkuList = new ArrayList<>();
        List<LogisticsErpOrderCreateBO.Sku> goodsSkuList = new ArrayList<>();

        // 判断是否有产品包
        List<CreateOrderFromExternalBO.Item> itemList = new ArrayList<>();
        erpSkuList.forEach(erpSku -> {
            if (ObjectUtils.isNotEmpty(erpSku.getPackageSn())) {
                CreateOrderFromExternalBO.Item item = new CreateOrderFromExternalBO.Item();
                item.setPackageSn(erpSku.getPackageSn());
                item.setCount(erpSku.getCount());
                item.setItemAmount(erpSku.getItemAmount());
                item.setThirdOrderSn(erpSku.getThirdOrderSn());
                itemList.add(item);
            } else {
                LogisticsErpOrderCreateBO.Sku sku = new LogisticsErpOrderCreateBO.Sku();
                sku.setStorageSku(erpSku.getSku());
                sku.setNums(erpSku.getCount());
                createSkuList.add(sku);
            }
        });

        // 有产品包信息、是普通订单 生成业务订单
        if (ObjectUtils.isNotEmpty(itemList) && record.getErpOrderType().equals(ErpRecordOrderTypeEnum.NORMAL.getValue())) {
            CreateOrderFromExternalBO createOrderBO = new CreateOrderFromExternalBO();
            createOrderBO.setThirdOrderSn(record.getThirdOrderSn());
            createOrderBO.setPaidAmount(record.getPaidAmount());
            createOrderBO.setSendName(record.getSendName());
            createOrderBO.setSendArea(record.getSendArea());
            createOrderBO.setSendAddress(record.getSendAddress());
            createOrderBO.setPhone(record.getSendPhone());
            createOrderBO.setLogisticCompany(record.getExpressCorp());
            createOrderBO.setLogisticNumber(record.getExpressNumber());
            createOrderBO.setSendTime(record.getDeliveryTime());
            createOrderBO.setItems(itemList);

            try {
                List<ProductOrderExternalCreateVO> orderList = productOrderBusiness.createOrderFromExternal(createOrderBO);
                // 拆分erp业务单
                if (ObjectUtils.isNotEmpty(orderList)) {
                    orderList.forEach(order -> {
                        LambdaQueryWrapper<ErpOrder> wrapper = new LambdaQueryWrapper<ErpOrder>()
                                .eq(ErpOrder::getErpSn, record.getErpSn())
                                .eq(ErpOrder::getBusinessSn, order.getProductOrderSn());
                        ErpOrder erpOrderCheck = erpOrderService.getOneByWrapper(wrapper);
                        if (ObjectUtils.isEmpty(erpOrderCheck)) {
                            // 创建erp订单
                            ErpOrder erpOrder = new ErpOrder();
                            erpOrder.setErpSn(record.getErpSn());
                            erpOrder.setBusinessSn(order.getProductOrderSn());
                            erpOrderService.create(erpOrder);
                        }
                        LogisticsErpOrderCreateBO.Sku sku = new LogisticsErpOrderCreateBO.Sku();
                        sku.setSku(order.getSkuSn());
                        sku.setNums(1);
                        goodsSkuList.add(sku);
                    });
                }
            } catch (Throwable e) {

                log.error("创建ErpRecord失败：" + e.getMessage());

                // 更新处理失败
                LambdaUpdateWrapper<ErpRecord> recordWrapper = new LambdaUpdateWrapper<>();
                recordWrapper.eq(ErpRecord::getErpSn, record.getErpSn())
                        .set(ErpRecord::getStatus, ErpRecordStatusEnum.FAIL.getValue())
                        .set(ErpRecord::getErrorMsg, "生成业务单失败：" + e.getMessage());

                erpRecordService.updateByWrapper(recordWrapper);

                String markdown = String.format("><font color=\"info\">ERP流水号</font>：%s 生成业务单失败\n", record.getErpSn()) +
                        String.format("><font color=\"warning\">异常原因</font>：%s\n", e.getMessage());
                erpOrderBusiness.erpOrderAlarm(markdown);
                ToolsHelper.throwException("生成业务单失败：" + e.getMessage());
            }
        } else {
            ErpOrder order = erpOrderService.getOneByColumn(record.getErpSn(), ErpOrder::getErpSn);
            if (ObjectUtils.isEmpty(order)) {
                // 创建erp订单
                ErpOrder erpOrder = new ErpOrder();
                erpOrder.setErpSn(record.getErpSn());
                erpOrder.setBusinessSn(record.getThirdOrderSn());
                erpOrderService.create(erpOrder);
            }
        }

        // 处理发货记录
        if (record.getErpOrderSource().equals(ErpRecordOrderSourceEnum.YUNDA.getValue())) {
            try {
                if (ObjectUtils.isEmpty(createSkuList)) {
                    ToolsHelper.throwException("无发货商品");
                }
                LogisticsErpOrderCreateBO createBO = new LogisticsErpOrderCreateBO();
                createBO.setStorageCode(StorageCodeEnum.YUNDA.getValue());
                createBO.setErpSn(record.getErpSn());
                createBO.setOrderSn(record.getThirdOrderSn());
                createBO.setOrderType(ErpRecordOrderTypeEnum.logisticsOrderTypeMap.getOrDefault(record.getErpOrderType(), LogisticsOrderTypeEnum.JD_APPLY.getValue()));
                createBO.setSendArea(record.getSendArea());
                createBO.setSendName(record.getSendName());
                createBO.setSendPhone(record.getSendPhone());
                createBO.setSendAddress(record.getSendAddress());
                createBO.setExpressNumber(record.getExpressNumber());
                createBO.setExpressCorp(record.getExpressCorp());
                createBO.setDeliveryTime(record.getDeliveryTime());
                createBO.setLogisticsCode(confirmYundaBO.getExpressCorpNo());
                createBO.setShipperNo(StringUtils.isNotEmpty(confirmYundaBO.getExpressCorpNo()) ? confirmYundaBO.getExpressCorpNo().toLowerCase() : confirmYundaBO.getExpressCorp());
                createBO.setWarehouseNo(confirmYundaBO.getWarehouseNo());
                createBO.setSkuList(createSkuList);
                createBO.setGoodsSkuList(goodsSkuList);

                handleLogistics(record, createBO, confirmYundaBO);
            } catch (Throwable e) {
                // 更新处理失败
                LambdaUpdateWrapper<ErpRecord> recordWrapper = new LambdaUpdateWrapper<>();
                recordWrapper.eq(ErpRecord::getErpSn, record.getErpSn())
                        .set(ErpRecord::getStatus, ErpRecordStatusEnum.FAIL.getValue())
                        .set(ErpRecord::getErrorMsg, e.getMessage());

                erpRecordService.updateByWrapper(recordWrapper);

                String markdown = String.format("><font color=\"info\">ERP流水号</font>：%s 生成发货单失败\n", record.getErpSn()) +
                        String.format("><font color=\"warning\">异常原因</font>：%s\n", e.getMessage());
                erpOrderBusiness.erpOrderAlarm(markdown);
                ToolsHelper.throwException(e.getMessage());
            }
        }

        // 更新erp记录
        LambdaUpdateWrapper<ErpRecord> recordWrapper = new LambdaUpdateWrapper<>();
        recordWrapper.eq(ErpRecord::getErpSn, record.getErpSn())
                .set(ErpRecord::getStatus, ErpRecordStatusEnum.SUCCESS.getValue())
                .set(ErpRecord::getErrorMsg, "");

        erpRecordService.updateByWrapper(recordWrapper);
    }

    private void handleLogistics(ErpRecord record, LogisticsErpOrderCreateBO createBO, TaskLogisticsConfirmYundaBO confirmYundaBO) {
        LambdaUpdateWrapper<ErpOrder> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(ErpOrder::getErpSn, record.getErpSn());
        try {
            // 处理发货单
            Logistics logistics = logisticsBusiness.createErpLogisticsOrder(createBO);

            // 处理出库单
            logisticsBusiness.createErpExWarehouse(createBO);

            // 更新erp订单发货信息
            wrapper.set(ErpOrder::getLogisticsSn, logistics.getLogisticsSn());
            wrapper.set(ErpOrder::getErrorMsg, "");

            // 处理序列号（发货单生成成功后）
            confirmYundaBO.setLogisticsSn(logistics.getLogisticsSn());
            erpOrderBusiness.handleDeliverySerials(record, confirmYundaBO, logistics);

            // 物流订阅
            ExpressSubscribeDTO subscribeDTO = new ExpressSubscribeDTO();
            subscribeDTO.setExpressCode(logistics.getStorageCode());
            subscribeDTO.setOrderSn(logistics.getLogisticsSn());
            subscribeDTO.setExpressNumber(logistics.getExpressNumber());
            subscribeDTO.setName(logistics.getSendName());
            subscribeDTO.setAddress(logistics.getSendAddress());
            subscribeDTO.setMobile("");
            subscribeDTO.setProvince("无");
            subscribeDTO.setCity("无");
            subscribeDTO.setArea("无");
            expressBusiness.subscribe(subscribeDTO);
        } catch (Exception e) {
            wrapper.set(ErpOrder::getErrorMsg, "创建发货单失败：" + e.getMessage());
            ToolsHelper.throwException("创建发货单失败：" + e.getMessage());
        } finally {
            erpOrderService.updateByWrapper(wrapper);
        }
    }

}
