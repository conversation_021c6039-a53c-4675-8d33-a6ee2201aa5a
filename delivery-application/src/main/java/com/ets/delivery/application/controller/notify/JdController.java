package com.ets.delivery.application.controller.notify;

import com.ets.delivery.application.app.business.ExpressBusiness;
import com.ets.delivery.application.app.thirdservice.request.jd.JdExpressNotifyDTO;
import com.ets.delivery.application.app.thirdservice.response.jd.JdExpressNotifyVO;
import com.ets.delivery.application.common.consts.express.ExpressCodeEnum;
import com.ets.delivery.application.common.dto.express.ExpressNotifyDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@Slf4j
@RestController
@RequestMapping("/notify/jd")
public class JdController {

    @Autowired
    private ExpressBusiness expressBusiness;

    @RequestMapping("/expressNotify")
    public JdExpressNotifyVO callbackNotify(JdExpressNotifyDTO notifyDTO) {
        log.info("【物流轨迹推送】【京东】{}", notifyDTO);

        try {
            ExpressNotifyDTO expressNotifyDTO = new ExpressNotifyDTO();
            expressNotifyDTO.setExpressCode(ExpressCodeEnum.JD_CLOUD.getValue());
            expressNotifyDTO.setNotifyData(notifyDTO);

            expressBusiness.expressNotify(expressNotifyDTO);
        } catch (Exception e) {
            log.error("【物流轨迹推送】异常：{}", e.getMessage());
        }
        return new JdExpressNotifyVO();
    }
}
