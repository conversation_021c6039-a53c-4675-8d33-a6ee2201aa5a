package com.ets.delivery.application.common.bo.logistics;

import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class LogisticsPageBO {

    private String issuerId;

    private List<String> storageCodeList;

    private List<String> logisticsSnList;
    private List<String> notLogisticsSnList;

    private String orderType;

    private String originOrderSn;

    private String orderSource;

    private String plateNo;

    private Integer status;

    private Integer deliveryStatus;
    private List<Integer> deliveryStatusList;

    private Integer notifyStatus;

    private String orderSn;

    private String expressNumber;

    private Integer expressStatus;

    private String reason;

    private String operator;
    private List<String> operatorList;

    private String sendPhone;

    private List<String> storageSkuList;
    
    /**
     * 序列号查询参数
     */
    private String serialNo;

    private LocalDateTime createStartTime;

    private LocalDateTime createEndTime;

    private LocalDateTime deliveryStartTime;

    private LocalDateTime deliveryEndTime;

    private LocalDateTime pushStartTime;

    private LocalDateTime pushEndTime;

    /**
     * 页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 分页大小
     */
    @Max(value = 100, message = "每页最多100条")
    private Integer pageSize = 20;

}
