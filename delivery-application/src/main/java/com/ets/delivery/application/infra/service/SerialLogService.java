package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.delivery.application.common.dto.serial.AdminSerialLogListDTO;
import com.ets.delivery.application.infra.entity.SerialLog;
import com.ets.delivery.application.infra.mapper.SerialLogMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

/**
 * <p>
 * 序列号流水表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
@Service
@DS("db-delivery")
public class SerialLogService extends BaseService<SerialLogMapper, SerialLog> {

    /**
     * 构建序列号日志对象（用于批量操作）
     */
    public SerialLog buildLog(String serialNo, Integer status, String businessSource, String businessSn, String operator, String remark, String inventoryType) {
        SerialLog serialLog = new SerialLog();
        serialLog.setSerialNo(serialNo);
        serialLog.setStatus(status);
        serialLog.setBusinessSource(businessSource);
        serialLog.setBusinessSn(businessSn);
        serialLog.setOperator(operator);
        serialLog.setRemark(remark);
        serialLog.setInventoryType(inventoryType);
        serialLog.setCreatedAt(LocalDateTime.now());
        serialLog.setUpdatedAt(LocalDateTime.now());
        return serialLog;
    }

    /**
     * 分页查询序列号日志列表
     */
    public IPage<SerialLog> getPage(AdminSerialLogListDTO listDTO) {
        Wrapper<SerialLog> wrapper = Wrappers.<SerialLog>lambdaQuery()
                .eq(StringUtils.isNotEmpty(listDTO.getSerialNo()), SerialLog::getSerialNo, listDTO.getSerialNo())
                .orderByDesc(SerialLog::getCreatedAt);
        return this.baseMapper.selectPage(new Page<>(listDTO.getPageNum(), listDTO.getPageSize()), wrapper);
    }
}