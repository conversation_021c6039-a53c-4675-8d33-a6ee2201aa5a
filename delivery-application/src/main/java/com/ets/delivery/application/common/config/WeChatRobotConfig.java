package com.ets.delivery.application.common.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "wechat-robot")
public class WeChatRobotConfig {

    private String storageAlarmKey;

    private String erpOrderAlarmKey;

    private String logisticsAlarmKey;

    private String pickUpAlarmKey;

    private String logisticsShipFailAlarmKey;

    private String serialAlarmKey;
}
