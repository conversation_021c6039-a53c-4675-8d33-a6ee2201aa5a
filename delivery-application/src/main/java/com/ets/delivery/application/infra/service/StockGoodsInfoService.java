package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.common.consts.stock.StockGoodsInfoStatusEnum;
import com.ets.delivery.application.infra.entity.StockGoodsInfo;
import com.ets.delivery.application.infra.mapper.StockGoodsInfoMapper;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 库存商品信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
@Service
@DS("db-issuer-admin-minor")
public class StockGoodsInfoService extends BaseService<StockGoodsInfoMapper, StockGoodsInfo> {

    @Override
    public int updateByWrapper(LambdaUpdateWrapper<StockGoodsInfo> lambdaUpdateWrapper) {
        lambdaUpdateWrapper.set(StockGoodsInfo::getUpdatedAt, ToolsHelper.getDateTime());

        return super.updateByWrapper(lambdaUpdateWrapper);
    }

    public List<StockGoodsInfo> getList(String stockSn, Integer stockType) {

        LambdaQueryWrapper<StockGoodsInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockGoodsInfo::getStockSn, stockSn)
                .eq(StockGoodsInfo::getStatus, StockGoodsInfoStatusEnum.NORMAL.getCode())
                .eq(StockGoodsInfo::getType, stockType);

        return getListByWrapper(wrapper);
    }

    public List<StockGoodsInfo> getStockSnListByGoods(String deliveryGoodsCode, Integer stockType, Integer limit) {

        LambdaQueryWrapper<StockGoodsInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockGoodsInfo::getDeliveryGoodsCode, deliveryGoodsCode)
                .eq(StockGoodsInfo::getStatus, StockGoodsInfoStatusEnum.NORMAL.getCode())
                .eq(StockGoodsInfo::getType, stockType)
                .last("LIMIT " + limit);

        return getListByWrapper(wrapper);
    }

    public StockGoodsInfo getByGoodsCode(String stockSn, String deliveryGoodsCode) {
        LambdaQueryWrapper<StockGoodsInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(StockGoodsInfo::getStockSn, stockSn)
                .eq(StockGoodsInfo::getStatus, StockGoodsInfoStatusEnum.NORMAL.getCode())
                .eq(StockGoodsInfo::getDeliveryGoodsCode, deliveryGoodsCode);

        return getOneByWrapper(wrapper);
    }

    public void updateNumber(Integer id, String numberInfo) {

        LambdaUpdateWrapper<StockGoodsInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(StockGoodsInfo::getId, id)
                .set(StockGoodsInfo::getNumberInfo, numberInfo);

        updateByWrapper(wrapper);
    }

    public void updateRealCount(String stockSn, String deliveryGoodsCode, Integer stockType, Integer realCount) {

        LambdaUpdateWrapper<StockGoodsInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(StockGoodsInfo::getDeliveryGoodsCode, deliveryGoodsCode)
                .eq(StockGoodsInfo::getType, stockType)
                .eq(StockGoodsInfo::getStockSn, stockSn)
                .setSql("`real_count` = `real_count` + " + realCount);

        updateByWrapper(wrapper);
    }


}
