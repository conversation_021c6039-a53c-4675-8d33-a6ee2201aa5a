package com.ets.delivery.application.common.config;

import cn.hutool.core.util.ObjectUtil;
import com.ets.delivery.application.common.dto.CategoryMapDTO;
import com.ets.delivery.application.common.dto.AppKeyParams;
import com.ets.delivery.application.common.dto.IssuerMapDTO;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

import java.util.List;
import java.util.Map;

@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "delivery-config")
public class DeliveryConfig {
    /*
     * 默认仓储编码
     */
    private String defaultStorageCode;

    /*
     * 默认物流方式
     */
    private String defaultExpressCorpCode;

    /**
     * 默认发货通知地址
     */
    private String defaultLogisticsNotifyUrl;

    /**
     * 快递单号错误通知订单类型
     */
    private List<String> expressNumberWrongOrderTypeList;

    /**
     * 通知发货异常订单类型列表
     */
    private List<String> notifyShipFailOrderTypeList;

    /**
     * 通过sku发货订单类型列表
     */
    private List<String> skuLogisticsOrderTypeList;

    /**
     * 秘钥配置
     */
    Map<String, AppKeyParams> appKeyParams;

    /**
     * 发卡方映射
     */
    Map<Integer, IssuerMapDTO> issuerMap;
    Map<String, CategoryMapDTO> categoryMap;

    /**
     * COS图片水印base64
     */
    private String watermarkBase64;

    public String getCnNameByIssuerId(Integer issuerId) {
        IssuerMapDTO issuerMap = this.issuerMap.get(issuerId);
        if (ObjectUtil.isNotEmpty(issuerMap)) {
            return issuerMap.getCnName();
        }
        return "";
    }

    public String getNameByIssuerId(Integer issuerId) {
        IssuerMapDTO issuerMap = this.issuerMap.get(issuerId);
        if (ObjectUtil.isNotEmpty(issuerMap)) {
            return issuerMap.getName();
        }
        return "";
    }
}
