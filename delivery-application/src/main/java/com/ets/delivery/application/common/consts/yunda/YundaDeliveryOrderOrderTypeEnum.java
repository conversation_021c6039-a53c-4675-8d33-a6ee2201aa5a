package com.ets.delivery.application.common.consts.yunda;

import com.ets.delivery.application.common.consts.erpRecord.ErpRecordOrderTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum YundaDeliveryOrderOrderTypeEnum {

    //JYCK=一般交易出库,HHCK=换货出库,BFCK=补发出库，QTCK=其他出库单
    JYCK("JYCK", ErpRecordOrderTypeEnum.NORMAL.getValue(), "一般交易出库"),
    HHCK("HHCK", ErpRecordOrderTypeEnum.AFTER_SALE.getValue(), "换货出库"),
    BFCK("BFCK", ErpRecordOrderTypeEnum.AFTER_SALE.getValue(), "补发出库"),
    QTCK("QTCK", ErpRecordOrderTypeEnum.AFTER_SALE.getValue(), "其他出库单");

    private final String value;
    private final Integer erpOrderType;
    private final String desc;
    public static final Map<String, String> map;
    public static final Map<String, Integer> erpOrderTypeMap;

    static {
        map = Arrays.stream(YundaDeliveryOrderOrderTypeEnum.values()).collect(Collectors.toMap(YundaDeliveryOrderOrderTypeEnum::getValue, YundaDeliveryOrderOrderTypeEnum::getDesc));
        erpOrderTypeMap = Arrays.stream(YundaDeliveryOrderOrderTypeEnum.values()).collect(Collectors.toMap(YundaDeliveryOrderOrderTypeEnum::getValue, YundaDeliveryOrderOrderTypeEnum::getErpOrderType));
    }
}
