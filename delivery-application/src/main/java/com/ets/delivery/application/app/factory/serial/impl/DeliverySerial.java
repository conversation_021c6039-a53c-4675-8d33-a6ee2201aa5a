package com.ets.delivery.application.app.factory.serial.impl;

import com.ets.delivery.application.common.bo.serial.SerialProcessBO;
import com.ets.delivery.application.common.consts.serial.SerialStatusEnum;
import com.ets.delivery.application.infra.entity.LogisticsSku;
import com.ets.delivery.application.infra.entity.LogisticsSkuSerial;
import com.ets.delivery.application.infra.service.LogisticsSkuSerialService;
import com.ets.delivery.application.infra.service.LogisticsSkuService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 发货序列号处理器
 */
@Slf4j
@Component
public class DeliverySerial extends SerialBase {
    
    @Autowired
    private LogisticsSkuService logisticsSkuService;
    
    @Autowired
    private LogisticsSkuSerialService logisticsSkuSerialService;
    

    
    @Override
    protected void setTargetStatus(SerialProcessBO processBO) {
        processBO.setTargetStatus(SerialStatusEnum.OUT_STOCK);
    }
    
    @Override
    protected void executeSpecificLogic(SerialProcessResult result, SerialProcessBO processBO) {
        // 处理不存在序列号的预警
        handleMissingSerialWarning(result.getNewSerials(), processBO);

        updateLogisticsSkuSerial(processBO.getSerialList(), processBO.getBusinessSn(), processBO.getStorageSku());
    }
    
    /**
     * 更新LogisticsSkuSerial表数据（发货处理）
     */
    private void updateLogisticsSkuSerial(List<String> serialList, String businessSn, String storageSku) {
        try {
            log.info("开始更新LogisticsSkuSerial表数据: businessSn={}, 序列号数量={}", businessSn, serialList.size());
            
            LogisticsSku logisticsSku = logisticsSkuService.getByStorageSku(businessSn, storageSku);
            if (logisticsSku == null) {
                log.warn("未找到发货记录商品：发货单={} sku={}", businessSn, storageSku);
                return;
            }
            
            // 批量创建LogisticsSkuSerial记录
            List<LogisticsSkuSerial> logisticsSkuSerials = serialList.stream()
                    .map(serialNo -> {
                        LogisticsSkuSerial logisticsSkuSerial = new LogisticsSkuSerial();
                        logisticsSkuSerial.setSerialNo(serialNo);
                        logisticsSkuSerial.setLogisticsSn(businessSn);
                        logisticsSkuSerial.setLogisticsSkuId(logisticsSku.getId());
                        logisticsSkuSerial.setCreatedAt(LocalDateTime.now());
                        logisticsSkuSerial.setUpdatedAt(LocalDateTime.now());
                        return logisticsSkuSerial;
                    })
                    .collect(Collectors.toList());
            
            if (!logisticsSkuSerials.isEmpty()) {
                logisticsSkuSerialService.saveBatch(logisticsSkuSerials);
                log.info("成功保存LogisticsSkuSerial记录: 数量={}", logisticsSkuSerials.size());
            }
            
        } catch (Exception e) {
            log.error("更新LogisticsSkuSerial表数据失败: businessSn={}", businessSn, e);
        }
    }
}