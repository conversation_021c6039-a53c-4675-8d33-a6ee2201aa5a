package com.ets.delivery.application.common.consts.storageRecord;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum GoodsTypeEnum {


    CARD_OBU(1, "ETC卡+OBU", "card_obu"),

    CARD(2, "ETC单卡", "card"),

    OBU(3, "单OBU", "obu"),

    BROKEN(4, "ETC设备破损", ""),

    UNIT(6, "单片式设备", "card_obu"),

    OTHER(5, "非高灯设备", ""),

    GOODS(7, "其他商品", "");

    private final Integer value;
    private final String desc;
    private final String type;

    public static final Map<Integer, String> typeMap;
    public static final Map<Integer, String> map;

    static {
        typeMap = Arrays.stream(GoodsTypeEnum.values()).collect(Collectors.toMap(GoodsTypeEnum::getValue, GoodsTypeEnum::getType));
        map = Arrays.stream(GoodsTypeEnum.values()).collect(Collectors.toMap(GoodsTypeEnum::getValue, GoodsTypeEnum::getDesc));
    }

    public static String getDescByCode(int code) {

        for (GoodsTypeEnum node : GoodsTypeEnum.values()) {
            if (node.getValue() == code) {
                return node.getDesc();
            }
        }

        return "";
    }

    public static String getTypeByValue(Integer value) {

        for (GoodsTypeEnum node : GoodsTypeEnum.values()) {
            if (node.getValue().equals(value)) {
                return node.getType();
            }
        }

        return "";
    }

}
