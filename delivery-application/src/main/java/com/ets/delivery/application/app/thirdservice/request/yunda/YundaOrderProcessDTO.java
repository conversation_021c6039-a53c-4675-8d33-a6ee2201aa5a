package com.ets.delivery.application.app.thirdservice.request.yunda;

import lombok.Data;

import jakarta.xml.bind.annotation.XmlAccessType;
import jakarta.xml.bind.annotation.XmlAccessorType;
import jakarta.xml.bind.annotation.XmlElement;
import jakarta.xml.bind.annotation.XmlRootElement;

@Data
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.FIELD)
public class YundaOrderProcessDTO {

    @XmlElement(name = "order")
    private Order order;

    @XmlElement(name = "process")
    private Process process;

    @Data
    public static class Order {
        String orderCode;
        String orderId;
        String orderType;
        String warehouseCode;
    }

    @Data
    public static class Process {
        String processStatus;
        String operatorCode;
        String operatorName;
        String expressCode;
        String operateTime;
        String operateInfo;
        String remark = "";
        ExtendProps extendProps;

        @Data
        public static class ExtendProps {
            Integer code;
        }
    }
}
