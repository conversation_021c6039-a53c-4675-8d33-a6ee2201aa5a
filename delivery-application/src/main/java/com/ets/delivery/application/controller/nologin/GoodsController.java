package com.ets.delivery.application.controller.nologin;

import com.ets.common.JsonResult;
import com.ets.common.util.ListUtils;
import com.ets.delivery.application.app.business.AlarmBusiness;
import com.ets.delivery.application.app.business.ManageBusiness;
import com.ets.delivery.application.app.business.storageMina.StorageBusiness;
import com.ets.delivery.application.common.dto.alarm.LogisticsAlarmDTO;
import com.ets.delivery.application.common.dto.alarm.StockAlarmDTO;
import com.ets.delivery.application.common.dto.manage.ManageStorageSkuMapEditDTO;
import com.ets.delivery.application.common.dto.manage.StorageMapEditBySkuDTO;
import com.ets.delivery.application.common.dto.storage.GetStorageInfoDTO;
import com.ets.delivery.application.common.dto.storageMap.GetStorageSkuBySkuSnDTO;
import com.ets.delivery.application.common.dto.storageMap.GetListByStorageSkuDTO;
import com.ets.delivery.application.common.vo.alarm.LogisticsAlarmVO;
import com.ets.delivery.application.common.vo.alarm.StockAlarmVO;
import com.ets.delivery.application.common.vo.storageMap.StorageSkuMapVO;
import com.ets.delivery.application.infra.entity.Storage;
import com.ets.delivery.application.infra.entity.StorageSkuMap;
import com.ets.delivery.application.infra.service.StorageSkuMapService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

import java.util.HashMap;
import java.util.List;

@RestController
@RequestMapping("/no-login/goods")
public class GoodsController {

    @Autowired
    private AlarmBusiness alarmBusiness;

    @Autowired
    private StorageBusiness storageBusiness;

    @Autowired
    private StorageSkuMapService storageSkuMapService;

    @Autowired
    private ManageBusiness manageBusiness;

    @RequestMapping("/stockAlarm")
    public JsonResult<StockAlarmVO> stockAlarm(@RequestBody @Valid StockAlarmDTO stockAlarmDTO) {
        return JsonResult.ok(alarmBusiness.stockAlarm(stockAlarmDTO));
    }

    @RequestMapping("/logisticsAlarm")
    public JsonResult<LogisticsAlarmVO> logisticsAlarm(@RequestBody @Valid LogisticsAlarmDTO logisticsAlarmDTO) {
        return JsonResult.ok(alarmBusiness.logisticsAlarm(logisticsAlarmDTO));
    }

    @RequestMapping("/getStorageInfo")
    public JsonResult<Storage> getStorageInfo(@RequestBody @Valid GetStorageInfoDTO dto) {

        return JsonResult.ok(storageBusiness.getByStorageCode(dto.getStorageCode()));
    }

    @PostMapping("/getListByStorageSku")
    public JsonResult<List<String>> getListByStorageSku(@RequestBody @Valid GetListByStorageSkuDTO dto) {

        List<StorageSkuMap> list = storageSkuMapService.getListByStorageSku(dto.getStorageCode(), dto.getStorageSku());

        return JsonResult.ok(ListUtils.getColumnList(list, StorageSkuMap::getSku));
    }

    @PostMapping("/getStorageSkuBySkuSn")
    public JsonResult<HashMap<String, StorageSkuMapVO>> getStorageSkuBySkuSn(@RequestBody @Valid GetStorageSkuBySkuSnDTO dto) {

        List<StorageSkuMap> list = storageSkuMapService.getListByStorageCodeSku(dto.getStorageCode(), dto.getSkuSnList());

        HashMap<String, StorageSkuMapVO> result = new HashMap<>();
        list.forEach(skuMap -> {
            StorageSkuMapVO vo = new StorageSkuMapVO();
            vo.setStorageSku(skuMap.getStorageSku());
            vo.setStorageSkuName(skuMap.getGoodsName());
            vo.setStatus(skuMap.getStatus());

            result.put(skuMap.getSku(), vo);
        });

        return JsonResult.ok(result);
    }

    @PostMapping("/editStorageSkuMap")
    public JsonResult<Object> editStorageSkuMap(@RequestBody @Validated StorageMapEditBySkuDTO dto){

        manageBusiness.editBySkuSn(dto);

        return JsonResult.ok();
    }

}
