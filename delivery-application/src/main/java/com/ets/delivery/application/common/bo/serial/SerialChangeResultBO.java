package com.ets.delivery.application.common.bo.serial;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 序列号变更检测结果业务对象
 * 用于封装序列号变更检测的结果信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SerialChangeResultBO {

    /**
     * 原有序列号列表
     */
    private List<String> originalSerialList;

    /**
     * 新序列号列表（已去重处理）
     */
    private List<String> newSerialList;

    /**
     * 是否发生变更
     */
    private boolean changed;
}
