package com.ets.delivery.application.controller.testing;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.factory.task.TaskFactory;
import com.ets.delivery.application.app.thirdservice.business.KdBusiness;
import com.ets.delivery.application.common.bo.express.ExpressBO;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.redisson.template.DistributedLockTemplate;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.concurrent.TimeUnit;

@Slf4j
@RestController
@RequestMapping("/no-login/zjh")
public class ZjhController {

    @Autowired
    private DistributedLockTemplate distributedLockTemplate;

    @Autowired
    private KdBusiness kdBusiness;




    @RequestMapping("/task")
    public JsonResult<Object> task(@RequestParam(value = "referSn", required = false) String referSn) {

        TaskFactory.createAndPush(TaskRecordReferTypeEnum.TASK_LOGISTICS_SENDBACK_CHECK, referSn);

        return JsonResult.ok();
    }

    @RequestMapping("/tryLock")
    public JsonResult<Object> tryLock() {

        String lockKey = "tryLockTest";
        distributedLockTemplate.tryLock(lockKey, 10, 20, TimeUnit.SECONDS, () -> {
            log.info("xxxx");
            return null;
        });

        return JsonResult.ok();
    }

    @PostMapping("/expressSubscribe")
    public JsonResult<Object> expressSubscribe(@RequestBody ExpressBO expressBO) {

        return JsonResult.ok(kdBusiness.subscribe(expressBO));
    }
}
