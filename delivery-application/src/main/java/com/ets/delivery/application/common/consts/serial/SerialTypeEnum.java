package com.ets.delivery.application.common.consts.serial;

import com.ets.delivery.application.app.factory.serial.impl.DeliverySerial;
import com.ets.delivery.application.app.factory.serial.impl.ReturnSerial;
import com.ets.delivery.application.app.factory.serial.impl.SerialBase;
import com.ets.delivery.application.app.factory.serial.impl.StockInSerial;
import com.ets.delivery.application.app.factory.serial.impl.StockOutSerial;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum SerialTypeEnum {

    RETURN("RETURN", ReturnSerial.class, "寄回"),
    STOCK_IN("STOCK_IN", StockInSerial.class, "入库"),
    STOCK_OUT("STOCK_OUT", StockOutSerial.class, "出库"),
    DELIVERY("DELIVERY", DeliverySerial.class, "发货");

    private final String value;
    private final Class<? extends SerialBase> handler;
    private final String desc;
    
    public static final Map<String, String> map;
    public static final List<String> list;

    static {
        SerialTypeEnum[] enums = SerialTypeEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
        list = Stream.of(enums)
                .map(SerialTypeEnum::getValue)
                .collect(Collectors.toList());
    }

    public static boolean isValidValue(String serialType) {
        return list.contains(serialType);
    }

    public static List<Map<String, String>> getSelectOptions() {
        return Arrays.stream(SerialTypeEnum.values())
                .map(enumValue -> Map.of("label", enumValue.getDesc(), "value", enumValue.getValue()))
                .collect(Collectors.toList());
    }
}