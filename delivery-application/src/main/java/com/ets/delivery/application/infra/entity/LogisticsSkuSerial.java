package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
@Data
@TableName("etc_logistics_sku_serial")
public class LogisticsSkuSerial extends BaseEntity<LogisticsSkuSerial> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 发货流水号
     */
    private String logisticsSn;

    /**
     * 发货商品记录id
     */
    private Integer logisticsSkuId;

    /**
     * 序列号
     */
    private String serialNo;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
