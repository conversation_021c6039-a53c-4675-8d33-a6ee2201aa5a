package com.ets.delivery.application.app.thirdservice.request.yunda;

import lombok.Data;

import jakarta.xml.bind.annotation.*;
import java.util.List;

@Data
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.FIELD)
public class StockInConfirmDTO {

    @XmlElement(name = "entryOrder")
    private EntryOrder entryOrder;

    @XmlElementWrapper(name = "orderLines")
    @XmlElement(name = "orderLine")
    private List<OrderLine> orderLines;

    @Data
    public static class EntryOrder {
        Integer totalOrderLines;
        String entryOrderCode;
        String ownerCode;
        String warehouseCode;
        String entryOrderId;
        String entryOrderType;
        String outBizCode;
        Integer confirmType;
        String status;
        String operateTime;
        String remark;
    }

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class OrderLine {
        String outBizCode;
        String orderLineNo;
        String ownerCode;
        String itemCode;
        String itemId;
        String itemName;
        String inventoryType;
        Integer planQty;
        Integer actualQty;
        String batchCode;
        String productDate;
        String expireDate;
        String produceCode;
        String remark;

        @XmlElementWrapper(name = "batchs")
        @XmlElement(name = "batch")
        List<Batch> batchs;

        @Data
        public static class Batch {
            String batchCode;
            String productDate;
            String expireDate;
            String produceCode;
            String inventoryType;
            Integer actualQty;
        }

        @XmlElementWrapper(name = "snList")
        @XmlElement(name = "sn")
        List<String> sn;
    }

}
