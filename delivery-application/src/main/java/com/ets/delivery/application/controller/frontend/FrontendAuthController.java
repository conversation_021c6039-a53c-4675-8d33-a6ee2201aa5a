package com.ets.delivery.application.controller.frontend;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.business.storageMina.AuthBusiness;
import com.ets.delivery.application.common.vo.auth.AuthUserVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/frontend/auth")
public class FrontendAuthController {

    @Autowired
    private AuthBusiness authBusiness;

    @PostMapping("/loginByUserName")
    public JsonResult<AuthUserVO> loginByUserName(
            @RequestParam(value = "userName") String userName,
            @RequestParam(value = "password") String password
    ) {

        return JsonResult.ok(authBusiness.loginByUserName(userName, password));
    }

}
