package com.ets.delivery.application.controller.notify;

import com.alibaba.fastjson.JSON;
import com.ets.common.JsonResult;
import com.ets.delivery.application.app.business.ExpressBusiness;
import com.ets.delivery.application.app.thirdservice.request.kd.KdExpressNotifyDTO;
import com.ets.delivery.application.app.thirdservice.response.kd.KdSubscribeVO;
import com.ets.delivery.application.common.consts.express.ExpressCodeEnum;
import com.ets.delivery.application.common.dto.express.ExpressNotifyDTO;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/notify/kd100")
public class KdController {

    @Autowired
    private ExpressBusiness expressBusiness;

    @RequestMapping("/expressNotify")
    public JsonResult<Void> expressNotify(@RequestBody @Valid KdExpressNotifyDTO notifyDTO) {
        log.info("【物流轨迹推送】【快递100】{}", notifyDTO);

        try {
            ExpressNotifyDTO expressNotifyDTO = new ExpressNotifyDTO();
            expressNotifyDTO.setExpressCode(ExpressCodeEnum.KD100.getValue());
            expressNotifyDTO.setNotifyData(notifyDTO);

            expressBusiness.expressNotify(expressNotifyDTO);
        } catch (Exception e) {
            log.error("【物流轨迹推送】异常：{}", e.getMessage());
        }
        return JsonResult.ok();
    }

    @PostMapping(value = "/express-notify", consumes = MediaType.APPLICATION_FORM_URLENCODED_VALUE)
    public KdSubscribeVO expressNotifyForm(@RequestParam(value = "param") String param) {
        
        log.info("【物流轨迹推送】【快递100】表单格式，param：{}", param);

        try {
            if (StringUtils.isEmpty(param)) {
                log.warn("【物流轨迹推送】【快递100】表单格式请求参数为空");
                KdSubscribeVO errorVo = new KdSubscribeVO();
                errorVo.setResult(false);
                errorVo.setReturnCode("400");
                errorVo.setMessage("参数为空");
                return errorVo;
            }

            // 将表单参数转换为DTO对象
            KdExpressNotifyDTO.ResultParam resultParam = JSON.parseObject(param, KdExpressNotifyDTO.ResultParam.class);
            log.info("【物流轨迹推送】【快递100】表单解析后：{}", resultParam);
            KdExpressNotifyDTO notifyDTO = new KdExpressNotifyDTO();
            notifyDTO.setParam(resultParam);

            ExpressNotifyDTO expressNotifyDTO = new ExpressNotifyDTO();
            expressNotifyDTO.setExpressCode(ExpressCodeEnum.KD100.getValue());
            expressNotifyDTO.setNotifyData(notifyDTO);

            expressBusiness.expressNotify(expressNotifyDTO);
        } catch (Exception e) {
            log.error("【物流轨迹推送】表单格式异常：{}", e.getMessage());
        }

        KdSubscribeVO vo = new KdSubscribeVO();
        vo.setResult(true);
        vo.setReturnCode("200");
        vo.setMessage("成功");
        return vo;
    }
}
