package com.ets.delivery.application.app.business;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.common.RequestHelper;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.factory.importFile.ImportFileFactory;
import com.ets.delivery.application.app.factory.importFile.impl.ImportFileBase;
import com.ets.delivery.application.app.factory.task.TaskFactory;
import com.ets.delivery.application.common.consts.importFile.ImportFileImportTypeEnum;
import com.ets.delivery.application.common.consts.importFile.ImportFileUploadStatusEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsDeliveryStatusEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsNotifyStatusEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsOperateTypeEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsStatusEnum;
import com.ets.delivery.application.common.consts.logisticsExpress.ImportLogisticsExpressStatusEnum;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.delivery.application.common.dto.logisticsExpress.LogisticsExpressBatchSaveDTO;
import com.ets.delivery.application.common.dto.logisticsExpress.LogisticsExpressImportDetailDeleteDTO;
import com.ets.delivery.application.common.dto.logisticsExpress.LogisticsExpressImportListDTO;
import com.ets.delivery.application.common.dto.task.TaskRecordDTO;
import com.ets.delivery.application.common.vo.logisticsExpress.LogisticsExpressImportListVO;
import com.ets.delivery.application.common.vo.logisticsExpress.LogisticsExpressImportVO;
import com.ets.delivery.application.infra.entity.ImportFileRecord;
import com.ets.delivery.application.infra.entity.ImportLogisticsExpressDetail;
import com.ets.delivery.application.infra.entity.Logistics;
import com.ets.delivery.application.infra.service.ImportFileRecordService;
import com.ets.delivery.application.infra.service.ImportLogisticsExpressDetailService;
import com.ets.delivery.application.infra.service.LogisticsLogService;
import com.ets.delivery.application.infra.service.LogisticsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 发货快递单导入业务类
 */
@Slf4j
@Component
public class LogisticsExpressImportBusiness {

    @Autowired
    private ImportFileRecordService importFileRecordService;

    @Autowired
    private ImportLogisticsExpressDetailService importLogisticsExpressDetailService;

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private LogisticsLogService logisticsLogService;

    public LogisticsExpressImportVO logisticsExpressImport(MultipartFile file) {
        ImportFileBase deliveryImport = ImportFileFactory.create(ImportFileImportTypeEnum.LOGISTICS_EXPRESS_IMPORT);

        // 检查文件
        deliveryImport.importFileCheck(file);

        // 初始化导入记录
        ImportFileRecord importFileRecord = deliveryImport.initImportRecord(file.getOriginalFilename(), ImportFileImportTypeEnum.LOGISTICS_EXPRESS_IMPORT.getType());

        // 读取Excel文件
        deliveryImport.importFile(file, importFileRecord);

        LogisticsExpressImportVO importVO = new LogisticsExpressImportVO();
        importVO.setBatchNo(importFileRecord.getBatchNo());
        return importVO;
    }

    public IPage<LogisticsExpressImportListVO> getImportDataList(LogisticsExpressImportListDTO logisticsExpressImportListDTO) {
        ImportFileRecord record = importFileRecordService.getByBatchNo(logisticsExpressImportListDTO.getBatchNo());
        if (ObjectUtils.isEmpty(record)) {
            ToolsHelper.throwException("上传记录不存在");
        }
        if (record.getUploadStatus().equals(ImportFileUploadStatusEnum.FAIL.getValue())) {
            ToolsHelper.throwException(record.getErrorMsg());
        }
        IPage<ImportLogisticsExpressDetail> page = importLogisticsExpressDetailService.getPage(logisticsExpressImportListDTO);
        return page.convert(detail -> BeanUtil.copyProperties(detail, LogisticsExpressImportListVO.class));
    }

    public void batchSave(LogisticsExpressBatchSaveDTO logisticsExpressBatchSaveDTO) {
        String username = RequestHelper.getAdminOperator();
        List<ImportLogisticsExpressDetail> saveList = importLogisticsExpressDetailService.getCanSaveList(logisticsExpressBatchSaveDTO.getBatchNo());
        if (CollectionUtils.isEmpty(saveList)) {
            ToolsHelper.throwException("没有可保存数据");
        }

        saveList.forEach(detail -> {
            try {
                Logistics logistics = logisticsService.getByLogisticsSn(detail.getLogisticsSn());
                if (ObjectUtils.isEmpty(logistics)) {
                    ToolsHelper.throwException("发货单不存在");
                }
                // 操作人一致性校验（兜底防护）
                if (StringUtils.isNotEmpty(logistics.getOperator())
                        && StringUtils.isNotEmpty(username)
                        && !logistics.getOperator().equals(username)) {
                    ToolsHelper.throwException("非同一个操作人不允许导入");
                }

                // 发货单状态校验（兜底防护）
                if (logistics.getStatus().equals(LogisticsStatusEnum.STATUS_CANCEL.getValue())
                        || logistics.getDeliveryStatus().equals(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_CANCEL.getValue())) {
                    ToolsHelper.throwException("发货单已取消，不允许导入");
                }
                if (ObjectUtils.isNotEmpty(logistics.getStatus()) && ObjectUtils.isNotEmpty(logistics.getDeliveryStatus())
                        && logistics.getStatus().equals(LogisticsStatusEnum.STATUS_NORMAL.getValue())
                        && !logistics.getDeliveryStatus().equals(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_PROCESSING.getValue())) {
                    ToolsHelper.throwException("发货单状态为正常且发货状态非处理中，不允许导入");
                }

                // 更新发货单快递信息与状态
                Logistics update = new Logistics();
                update.setId(logistics.getId());
                update.setExpressCorp(detail.getExpressCompany());
                update.setExpressNumber(detail.getExpressNumber());
                update.setDeliveryStatus(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_SHIPPED.getValue());
                update.setDeliveryTime(LocalDateTime.now());
                update.setDeliveryRemark("导入发货成功");
                update.setStatus(LogisticsStatusEnum.STATUS_NORMAL.getValue());
                update.setNotifyStatus(LogisticsNotifyStatusEnum.NOTIFY_STATUS_PROCESSING.getValue());
                update.setNotifyTime(null);
                update.setUpdatedAt(LocalDateTime.now());
                logisticsService.updateById(update);

                // 通知业务方已发货（异步）
                Map<String, Object> contentMap = new HashMap<>();
                contentMap.put("logistics_id", logistics.getId());
                String content = JSON.toJSONString(contentMap);

                TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
                taskRecordDTO.setReferSn(logistics.getLogisticsSn());
                taskRecordDTO.setReferType(TaskRecordReferTypeEnum.TASK_LOGISTICS_SHIP.getType());
                taskRecordDTO.setNotifyContent(content);
                TaskFactory.create(TaskRecordReferTypeEnum.TASK_LOGISTICS_SHIP.getType()).addAndPush(taskRecordDTO);

                // 更新导入明细为已保存
                ImportLogisticsExpressDetail updateDetail = new ImportLogisticsExpressDetail();
                updateDetail.setId(detail.getId());
                updateDetail.setRecordStatus(1); // 已保存
                updateDetail.setUpdatedAt(LocalDateTime.now());
                importLogisticsExpressDetailService.updateById(updateDetail);

                // 添加日志
                logisticsLogService.addLog(logistics.getId(),
                        LogisticsOperateTypeEnum.TYPE_DELIVERY.getCode(),
                        "【发货快递导入】批量更新快递信息 批次号 " + detail.getBatchNo(),
                        username);
            } catch (Throwable e) {
                log.error("【发货快递导入】批量保存失败 批次号：{} 导入记录：{}", detail.getBatchNo(), detail.getId(), e);
            }
        });
    }

    public void deleteImportRecord(LogisticsExpressImportDetailDeleteDTO deleteDTO) {
        ImportLogisticsExpressDetail detail = importLogisticsExpressDetailService.getById(deleteDTO.getImportDetailId());
        if (ObjectUtils.isEmpty(detail)) {
            ToolsHelper.throwException("导入记录不存在");
        }
        // 仅允许初始化状态(0)删除
        if (!Integer.valueOf(0).equals(detail.getRecordStatus())) {
            ToolsHelper.throwException("记录状态不允许删除");
        }
        // 已删除直接返回
        if (ImportLogisticsExpressStatusEnum.DELETED.getValue().equals(detail.getStatus())) {
            return;
        }
        detail.setStatus(ImportLogisticsExpressStatusEnum.DELETED.getValue());
        detail.setUpdatedAt(LocalDateTime.now());
        importLogisticsExpressDetailService.updateById(detail);
    }
}