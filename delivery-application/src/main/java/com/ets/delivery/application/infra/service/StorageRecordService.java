package com.ets.delivery.application.infra.service;

import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.common.consts.storageRecord.StorageRecordStatusEnum;
import com.ets.delivery.application.common.dto.storage.StorageRecordAdminListDTO;
import com.ets.delivery.application.common.dto.storage.StorageRecordEditDTO;
import com.ets.delivery.application.infra.entity.StorageRecord;
import com.ets.delivery.application.infra.mapper.StorageRecordMapper;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.LocalTime;

/**
 * <p>
 * 入库记录表 服务实现类
 * </p>
 *
 * <AUTHOR> @since 2023-07-10
 */
@Service
@DS("db-issuer-admin")
public class StorageRecordService extends BaseService<StorageRecordMapper, StorageRecord> {

    public StorageRecord getByRecordSn(String recordSn) {

        return getOneByColumn(recordSn, StorageRecord::getRecordSn);
    }

    public StorageRecord getByExpressNumber(String expressNumber) {

        return getOneByColumn(expressNumber, StorageRecord::getExpressNumber);
    }

    public void edit(StorageRecordEditDTO dto, StorageRecord storageRecord) {

        LambdaUpdateWrapper<StorageRecord> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(StorageRecord::getRecordSn, dto.getRecordSn())
                .set(StorageRecord::getStorageCode, dto.getStorageCode())
                .set(StorageRecord::getExpressNumber, dto.getExpressNumber())
                .set(StorageRecord::getGoodsType, dto.getGoodsType())
                .set(StorageRecord::getGoodsImages, dto.getGoodsImages())
                .set(StorageRecord::getReviceRemark, dto.getReviceRemark())
                .set(dto.getSkuInfo() != null, StorageRecord::getSkuInfo, JSON.toJSONString(dto.getSkuInfo()));

        updateByWrapper(wrapper);

        storageRecord.setExpressNumber(dto.getExpressNumber());
        storageRecord.setStorageCode(dto.getStorageCode());
        storageRecord.setGoodsType(dto.getGoodsType());
        storageRecord.setGoodsImages(dto.getGoodsImages());
        storageRecord.setReviceRemark(dto.getReviceRemark());
        if (dto.getSkuInfo() != null) {
            storageRecord.setSkuInfo(JSON.toJSONString(dto.getSkuInfo()));
        }
    }

    @Override
    public int updateByWrapper(LambdaUpdateWrapper<StorageRecord> lambdaUpdateWrapper) {
        lambdaUpdateWrapper.set(StorageRecord::getUpdatedAt, ToolsHelper.getDateTime());

        return super.updateByWrapper(lambdaUpdateWrapper);
    }

    public void updateReceiveInfo(String recordSn, boolean isSuccess, String remark) {

        Integer status = isSuccess ? StorageRecordStatusEnum.SUCCESS.getValue() : StorageRecordStatusEnum.FAILED.getValue();

        LambdaUpdateWrapper<StorageRecord> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(StorageRecord::getRecordSn, recordSn)
                .set(StorageRecord::getStatus, status)
                .set(! isSuccess, StorageRecord::getReviceRemark, remark)
                .set(StorageRecord::getReviceTime, LocalDateTime.now());

        updateByWrapper(wrapper);
    }


    public IPage<StorageRecord> getPage(StorageRecordAdminListDTO listDTO) {
        Wrapper<StorageRecord> wrapper = Wrappers.<StorageRecord>lambdaQuery()
                .eq(StringUtils.isNotEmpty(listDTO.getStorageCode()), StorageRecord::getStorageCode, listDTO.getStorageCode())
                .eq(StringUtils.isNotEmpty(listDTO.getExpressNumber()), StorageRecord::getExpressNumber, listDTO.getExpressNumber())
                .eq(ObjectUtils.isNotEmpty(listDTO.getStatus()), StorageRecord::getStatus, listDTO.getStatus())
                .ge(ObjectUtils.isNotEmpty(listDTO.getReceiveStartTime()), StorageRecord::getReviceTime,
                        ObjectUtils.isNotEmpty(listDTO.getReceiveStartTime()) ? listDTO.getReceiveStartTime().atTime(LocalTime.MIN) : null)
                .le(ObjectUtils.isNotEmpty(listDTO.getReceiveEndTime()), StorageRecord::getReviceTime,
                        ObjectUtils.isNotEmpty(listDTO.getReceiveEndTime()) ? listDTO.getReceiveEndTime().atTime(LocalTime.MAX) : null)
                .orderByDesc(StorageRecord::getCreatedAt);
        return this.baseMapper.selectPage(new Page<>(listDTO.getPageNum(), listDTO.getPageSize()), wrapper);
    }
}
