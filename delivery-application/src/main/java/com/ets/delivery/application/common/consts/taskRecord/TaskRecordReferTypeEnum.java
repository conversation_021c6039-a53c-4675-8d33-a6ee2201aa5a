package com.ets.delivery.application.common.consts.taskRecord;

import com.ets.delivery.application.app.factory.task.impl.*;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.*;

@AllArgsConstructor
@Getter
public enum TaskRecordReferTypeEnum {

    TASK_PICKUP_NOTIFY(TaskRecordReferTypeConstant.PICKUP_NOTIFY, TaskPickUpNotify.class, "上门取件通知"),
    TASK_LOGISTICS_CONFIRM_YUNDA(TaskRecordReferTypeConstant.LOGISTICS_CONFIRM_YUNDA, TaskYundaConfirm.class, "韵达发货单确认"),
    TASK_LOGISTICS_DELIVER_GOODS(TaskRecordReferTypeConstant.LOGISTICS_DELIVER_GOODS, TaskLogisticsDeliverGoods.class, "商品发货单发货"),
    TASK_LOGISTICS_SHIP_FAIL(TaskRecordReferTypeConstant.LOGISTICS_SHIP_FAIL, TaskLogisticsShipFail.class, "发货异常通知"),
    TASK_LOGISTICS_QUERY_ORDER(TaskRecordReferTypeConstant.LOGISTICS_QUERY_ORDER, TaskLogisticsQueryOrder.class, "出库单详情查询"),
    TASK_LOGISTICS_REJECT_CHECK(TaskRecordReferTypeConstant.LOGISTICS_REJECT_CHECK, TaskLogisticsRejectCheck.class, "发货拒收检查"),
    TASK_LOGISTICS_SHIP(TaskRecordReferTypeConstant.LOGISTICS_SHIP, TaskLogisticsShip.class, "发货通知"),
    TASK_LOGISTICS_DELIVER_GOODS_CONFIRM("logistics_deliver_goods_confirm", TaskLogisticsDeliverGoodsConfirm.class, "发货确认"),
    TASK_LOGISTICS_SENDBACK_CHECK("logistics_sendback_check_java", TaskSendBackCheck.class, "寄回件检查通知"),
    TASK_EXPRESS_SUBSCRIBE("express_subscribe", TaskExpressSubscribe.class, "物流订阅"),
    TASK_ERP_ORDER_HANDLE("erp_order_handle", TaskErpOrderHandle.class, "ERP订单处理"),
    TASK_ERP_ORDER_CONFIRM("erp_order_confirm", TaskErpOrderConfirm.class, "ERP订单确认"),
    TASK_RISK_RESULT_NOTIFY("risk_result_notify", TaskRiskResultNotify.class, "风控结果通知"),
    TASK_RISK_REVIEW_RESULT_NOTIFY("risk_review_result_notify", TaskRiskReviewResultNotify.class, "风控审核结果通知"),
    ;

    private final String type;
    private final Class<? extends TaskBase> job;
    private final String desc;

    public static TaskRecordReferTypeEnum getByType(String type) {

        for (TaskRecordReferTypeEnum node : TaskRecordReferTypeEnum.values()) {
            if (node.getType().equals(type)) {
                return node;
            }
        }
        return null;
    }

    public static List<String> getTypeList() {

        List<String> list = new ArrayList<>();

        for (TaskRecordReferTypeEnum node : TaskRecordReferTypeEnum.values()) {
            if (node.getJob() != null) {
                list.add(node.getType());
            }
        }

        return list;
    }
}
