package com.ets.delivery.application.app.disposer;

import com.ets.common.queue.BaseDisposer;
import com.ets.delivery.application.app.business.serial.SerialBusiness;
import com.ets.delivery.application.common.bo.serial.SerialProcessBO;
import com.ets.delivery.application.common.dto.serial.SerialProcessDTO;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 序列号异步处理器
 * 用于异步处理序列号的入库、出库等操作
 */
@Slf4j
@NoArgsConstructor
@Component(value = "SerialProcessJobBean")
public class SerialProcessDisposer extends BaseDisposer {

    @Autowired
    private SerialBusiness serialBusiness;

    public SerialProcessDisposer(Object params) {
        super(params);
    }

    @Override
    public String getJobBeanName() {
        return "SerialProcessJobBean";
    }

    @Override
    public void execute(Object content) throws Exception {
        SerialProcessDTO dto = super.getParamsObject(content, SerialProcessDTO.class);
        
        log.info("开始异步处理序列号，业务单号：{}，序列号数量：{}，处理类型：{}", 
                dto.getBusinessSn(), 
                dto.getSerialList() != null ? dto.getSerialList().size() : 0, 
                dto.getSerialType());

        try {
            // 构建序列号处理业务对象
            SerialProcessBO processBO = SerialProcessBO.builder()
                    .serialList(dto.getSerialList())
                    .businessSn(dto.getBusinessSn())
                    .storageSku(dto.getStorageSku())
                    .inventoryType(dto.getInventoryType())
                    .operator(dto.getOperator())
                    .remark(dto.getRemark())
                    .storageCode(dto.getStorageCode())
                    .businessSource(dto.getBusinessSource())
                    .build();

            // 调用序列号处理业务逻辑
            serialBusiness.processSerials(dto.getSerialType(), processBO);

            log.info("序列号异步处理成功，业务单号：{}，序列号数量：{}", 
                    dto.getBusinessSn(), 
                    dto.getSerialList() != null ? dto.getSerialList().size() : 0);

        } catch (Exception e) {
            log.error("序列号异步处理失败，业务单号：{}，序列号：{}，处理类型：{}，错误：{}", 
                    dto.getBusinessSn(), 
                    dto.getSerialList(), 
                    dto.getSerialType(), 
                    e.getMessage(), e);
            
            // 重新抛出异常，让消息队列进行重试
            throw new Exception("序列号异步处理失败: " + e.getMessage(), e);
        }
    }
}
