package com.ets.delivery.application.common.consts.stock;

import com.ets.delivery.application.common.vo.SelectOptionsVO;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
@AllArgsConstructor
public enum StockOutStatusEnum {

    // NEW-未开始处理,  ACCEPT-仓库接单 , PARTDELIVERED-部分发货完成,  DELIVERED-发货完成,  EXCEPTION-异常,  CANCELED-取消,  CLOSED-关闭,  REJECT-拒单,  CANCELEDFAIL-取消失败

    CREATED(0, "", "已创建"),

    NEW(1, "NEW", "未开始处理"),

    ACCEPT(2, "ACCEPT", "仓库接单"),

    PARTDELIVERED(3, "PARTDELIVERED", "部分发货完成"),

    DELIVERED(4, "DELIVERED", "发货完成"),

    EXCEPTION(5, "EXCEPTION", "异常"),

    CANCELED(6, "CANCELED", "取消"),

    CLOSED(7, "CLOSED", "关闭"),

    REJECT(8, "REJECT", "拒单"),

    CANCELEDFAIL(9, "CANCELEDFAIL", "取消失败");

    private final Integer code;
    private final String yunDaCode;
    private final String description;

    public static String getDescByCode(int code) {

        for (StockOutStatusEnum node : StockOutStatusEnum.values()) {

            if (node.getCode() == code) {
                return node.getDescription();
            }
        }

        return "";
    }

    public static StockOutStatusEnum getByCode(int code) {

        for (StockOutStatusEnum node : StockOutStatusEnum.values()) {

            if (node.getCode() == code) {
                return node;
            }
        }

        return null;
    }

    public static StockOutStatusEnum getByYunDaCode(String yunDaCode) {

        for (StockOutStatusEnum node : StockOutStatusEnum.values()) {

            if (node.getYunDaCode().equals(yunDaCode)) {
                return node;
            }
        }

        return null;
    }


    public static List<SelectOptionsVO> getSelectOptions() {

        List<SelectOptionsVO> selectOptionsVOList = new ArrayList<>();
        for (StockOutStatusEnum node : StockOutStatusEnum.values()) {
            selectOptionsVOList.add(new SelectOptionsVO(node.getCode().toString(),node.getDescription()));
        }

        return selectOptionsVOList;
    }

}
