package com.ets.delivery.application.app.factory.express.impl;

import com.ets.delivery.application.app.factory.express.IExpress;
import com.ets.delivery.application.common.dto.express.ExpressNotifyDTO;
import com.ets.delivery.application.common.dto.express.ExpressSubscribeDTO;
import com.ets.delivery.application.infra.entity.Express;

public abstract class ExpressBase implements IExpress {
    @Override
    public void subscribe(ExpressSubscribeDTO subscribeDTO) {

    }

    @Override
    public Express expressNotify(ExpressNotifyDTO notifyDTO) {
        return null;
    }

    @Override
    public Express expressQuery(Express express) {
        return express;
    }
}
