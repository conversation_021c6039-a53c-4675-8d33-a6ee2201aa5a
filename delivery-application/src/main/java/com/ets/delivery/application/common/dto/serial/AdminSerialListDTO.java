package com.ets.delivery.application.common.dto.serial;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import java.time.LocalDate;

@Data
public class AdminSerialListDTO {

    /**
     * 序列号
     */
    private String serialNo;

    /**
     * 仓库编码
     */
    private String storageCode;

    /**
     * 仓库SKU
     */
    private String storageSku;

    /**
     * 库存类型[字符串类型]
     */
    private String inventoryType;

    /**
     * 状态[1-在库 2-出库 3-发货 4-退回]
     */
    private Integer status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate createStartTime;
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private LocalDate createEndTime;

    /**
     * 页码
     */
    @Min(value = 1, message = "页码必须大于0")
    private Integer pageNum = 1;

    /**
     * 分页大小
     */
    @Max(value = 100, message = "每页最多100条")
    private Integer pageSize = 20;
}