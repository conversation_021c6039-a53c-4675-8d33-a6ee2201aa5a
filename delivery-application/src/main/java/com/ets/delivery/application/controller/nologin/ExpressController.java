package com.ets.delivery.application.controller.nologin;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.business.ExpressBusiness;
import com.ets.delivery.application.common.dto.express.ExpressSubscribeDTO;
import com.ets.delivery.application.common.dto.express.CheckExpressRejectDTO;
import com.ets.delivery.application.common.dto.express.ExpressSubscribeDirectlyDTO;
import com.ets.delivery.application.common.dto.express.ExpressSubscribeQueryDTO;
import com.ets.delivery.application.common.vo.express.ExpressSubscribeQueryVO;
import com.ets.delivery.feign.request.express.ExpressAbnormalStatusDTO;
import com.ets.delivery.feign.request.express.ExpressBatchQueryDTO;
import com.ets.delivery.feign.response.express.ExpressBatchQueryVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/no-login/express")
public class ExpressController {

    @Autowired
    ExpressBusiness expressBusiness;

    @RequestMapping("/batchQuery")
    public JsonResult<List<ExpressBatchQueryVO>> batchQuery(@RequestBody @Valid ExpressBatchQueryDTO batchQueryDTO) {
        return JsonResult.ok(expressBusiness.batchQuery(batchQueryDTO));
    }

    @RequestMapping("/subscribe")
    public JsonResult<?> subscribe(@RequestBody @Valid ExpressSubscribeDTO subscribeDTO) {
        expressBusiness.subscribe(subscribeDTO);
        return JsonResult.ok();
    }

    @RequestMapping("/subscribeQuery")
    public JsonResult<ExpressSubscribeQueryVO> subscribeQuery(@RequestBody @Valid ExpressSubscribeQueryDTO subscribeQueryDTO) {
        return JsonResult.ok(expressBusiness.subscribeQuery(subscribeQueryDTO));
    }

    @RequestMapping("/checkExpressReject")
    public JsonResult<?> checkExpressReject(@RequestBody @Valid CheckExpressRejectDTO expressRejectDTO) {
        expressBusiness.checkExpressReject(expressRejectDTO);
        return JsonResult.ok();
    }

    @RequestMapping("/isAbnormalStatus")
    public JsonResult<Boolean> isAbnormalStatus(@RequestBody @Valid ExpressAbnormalStatusDTO dto) {

        return JsonResult.ok(expressBusiness.isAbnormalStatus(dto.getExpressNumber(), dto.getPhone()));
    }

    @RequestMapping("/subscribeDirectly")
    public JsonResult<Object> subscribeDirectly(@RequestBody @Valid ExpressSubscribeDirectlyDTO subscribeDTO) {

        expressBusiness.subscribeExpressDirectly(subscribeDTO);

        return JsonResult.ok();
    }
}
