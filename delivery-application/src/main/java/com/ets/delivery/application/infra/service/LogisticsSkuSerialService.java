package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.delivery.application.infra.entity.LogisticsSkuSerial;
import com.ets.delivery.application.infra.mapper.LogisticsSkuSerialMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
@Service
@DS("db-delivery")
public class LogisticsSkuSerialService extends BaseService<LogisticsSkuSerialMapper, LogisticsSkuSerial> {

    /**
     * 根据序列号查找对应的发货记录
     * @param serialNo 序列号
     * @return 发货序列号关联记录列表
     */
    public List<LogisticsSkuSerial> getListBySerialNo(String serialNo) {
        if (StringUtils.isEmpty(serialNo)) {
            return List.of();
        }
        
        LambdaQueryWrapper<LogisticsSkuSerial> wrapper = Wrappers.<LogisticsSkuSerial>lambdaQuery()
                .eq(LogisticsSkuSerial::getSerialNo, serialNo)
                .orderByDesc(LogisticsSkuSerial::getCreatedAt);
        
        return this.baseMapper.selectList(wrapper);
    }
}
