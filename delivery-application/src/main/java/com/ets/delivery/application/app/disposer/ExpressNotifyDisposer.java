package com.ets.delivery.application.app.disposer;

import com.baomidou.mybatisplus.core.toolkit.ObjectUtils;
import com.ets.common.queue.BaseDisposer;
import com.ets.delivery.application.app.business.ExpressBusiness;
import com.ets.delivery.application.app.factory.express.ExpressFactory;
import com.ets.delivery.application.common.consts.express.ExpressStateEnum;
import com.ets.delivery.application.common.dto.express.ExpressNotifyDTO;
import com.ets.delivery.application.infra.entity.Express;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@NoArgsConstructor
@Component(value = "ExpressNotifyJobBean")
public class ExpressNotifyDisposer extends BaseDisposer {

    @Autowired
    private ExpressBusiness expressBusiness;

    public ExpressNotifyDisposer(Object params) {
        super(params);
    }

    @Override
    public String getJobBeanName() {
        return "ExpressNotifyJobBean";
    }

    @Override
    public void execute(Object content) {
        ExpressNotifyDTO notifyDTO = super.getParamsObject(content, ExpressNotifyDTO.class);

        try {
            Express express = ExpressFactory.create(notifyDTO.getExpressCode()).expressNotify(notifyDTO);
            // 物流签收事件
            if (ObjectUtils.isNotNull(express) && express.getState().equals(ExpressStateEnum.RECEIVED.getValue())) {
                expressBusiness.sendExpressReceived(express);
            }
        } catch (Throwable e) {
            log.error("【物流轨迹推送】【{}】异常：{}", notifyDTO.getExpressCode(), e.getLocalizedMessage());
        }
    }
}
