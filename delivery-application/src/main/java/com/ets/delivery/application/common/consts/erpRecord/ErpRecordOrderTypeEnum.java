package com.ets.delivery.application.common.consts.erpRecord;

import com.ets.delivery.application.common.consts.logistics.LogisticsOrderTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

@Getter
@AllArgsConstructor
public enum ErpRecordOrderTypeEnum {
    // erp订单类型[1-普通订单 2-售后订单]
    NORMAL(1, LogisticsOrderTypeEnum.JD_APPLY.getValue(), "普通订单"),
    AFTER_SALE(2, LogisticsOrderTypeEnum.SHOP_AFTER_SALES.getValue(), "售后订单");

    private final Integer value;
    private final String logisticsOrderType;
    private final String desc;
    public static final Map<Integer, String> map;
    public static final Map<Integer, String> logisticsOrderTypeMap;

    static {
        map = Arrays.stream(ErpRecordOrderTypeEnum.values()).collect(Collectors.toMap(ErpRecordOrderTypeEnum::getValue, ErpRecordOrderTypeEnum::getDesc));
        logisticsOrderTypeMap = Arrays.stream(ErpRecordOrderTypeEnum.values()).collect(Collectors.toMap(ErpRecordOrderTypeEnum::getValue, ErpRecordOrderTypeEnum::getLogisticsOrderType));
    }
}
