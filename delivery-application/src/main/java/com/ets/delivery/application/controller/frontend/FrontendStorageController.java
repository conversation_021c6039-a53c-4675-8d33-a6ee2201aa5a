package com.ets.delivery.application.controller.frontend;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.common.JsonResult;
import com.ets.common.annotation.CosSignAnnotation;
import com.ets.delivery.application.app.business.SendBackBusiness;
import com.ets.delivery.application.app.business.storageMina.AuthBusiness;
import com.ets.delivery.application.app.business.storageMina.StorageRecordBusiness;
import com.ets.delivery.application.common.annotation.MinaCheckAccessAnnotation;
import com.ets.delivery.application.common.dto.storage.StorageHistoryListDTO;
import com.ets.delivery.application.common.dto.storage.StorageRecordCreateDTO;
import com.ets.delivery.application.common.dto.storage.StorageRecordEditDTO;
import com.ets.delivery.application.common.vo.logisticsSendBack.SendBackCheckInfoVO;
import com.ets.delivery.application.common.vo.storage.StorageCheckResultVO;
import com.ets.delivery.application.common.vo.storage.StorageRecordGetDataVO;
import com.ets.delivery.application.common.vo.storage.StorageRecordVO;
import com.ets.delivery.application.infra.entity.StorageRecord;
import com.ets.delivery.application.infra.entity.User;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;

@RestController
@RequestMapping("/frontend/storage")
@MinaCheckAccessAnnotation
public class FrontendStorageController {

    @Autowired
    private StorageRecordBusiness storageRecordBusiness;

    @Autowired
    private AuthBusiness authBusiness;

    @Autowired
    private SendBackBusiness sendBackBusiness;

    @PostMapping("/getOptions")
    public JsonResult<HashMap<String, List<?>>> getOptions() {

        return JsonResult.ok(storageRecordBusiness.getOptions());
    }

    @PostMapping("/getUploadInfo")
    public JsonResult<JSONObject> getUploadInfo() {

        return JsonResult.ok(storageRecordBusiness.getUploadInfo());
    }

    @PostMapping("/create")
    public JsonResult<StorageCheckResultVO> create(@RequestBody @Valid StorageRecordCreateDTO dto) {

        User user = authBusiness.getLoginUser();

        return JsonResult.ok(storageRecordBusiness.create(dto, user));
    }

    @PostMapping("/edit")
    public JsonResult<StorageCheckResultVO> edit(@RequestBody @Valid StorageRecordEditDTO dto) {

        User user = authBusiness.getLoginUser();

        return JsonResult.ok(storageRecordBusiness.edit(dto, user));
    }

    @PostMapping("/getData")
    @CosSignAnnotation
    public JsonResult<StorageRecordGetDataVO> getData(@RequestParam(value = "recordSn") String recordSn) {

        return JsonResult.ok(storageRecordBusiness.getData(recordSn));
    }

    @PostMapping("/getHistoryList")
    @CosSignAnnotation
    public JsonResult<IPage<StorageRecordVO>> getHistoryList(@RequestBody @Valid StorageHistoryListDTO dto) {

        User user = authBusiness.getLoginUser();

        return JsonResult.ok(storageRecordBusiness.getHistoryList(dto, user));
    }

    @PostMapping("/getSendBackCheckInfo")
    public JsonResult<SendBackCheckInfoVO> getSendBackCheckInfo(
            @RequestParam(value = "expressNumber") String expressNumber,
            @RequestParam(value = "storageCode") String storageCode
    ) {

        return JsonResult.ok(sendBackBusiness.getStorageCheckInfo(expressNumber, storageCode));
    }

    @PostMapping("/retryNotify")
    public JsonResult<StorageRecord> retryNotify(@RequestParam(value = "sendBackSn") String sendBackSn) {

        sendBackBusiness.retryNotify(sendBackSn);

        return JsonResult.ok();
    }

}
