package com.ets.delivery.application.app.factory.serial;

import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.factory.serial.impl.SerialBase;
import com.ets.delivery.application.common.consts.serial.SerialTypeEnum;

import cn.hutool.extra.spring.SpringUtil;

public class SerialFactory {

    public static SerialBase create(SerialTypeEnum serialTypeEnum) {
        if (serialTypeEnum == null) {
            ToolsHelper.throwException("序列号处理类型不能为空");
        }

        return SpringUtil.getBean(serialTypeEnum.getHandler());
    }
}