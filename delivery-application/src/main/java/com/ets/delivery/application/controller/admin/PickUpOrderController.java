package com.ets.delivery.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.business.ExpressBusiness;
import com.ets.delivery.application.app.business.LogisticsBusiness;
import com.ets.delivery.application.app.business.PickUpBusiness;
import com.ets.delivery.application.app.factory.express.impl.JdCloudExpressManage;
import com.ets.delivery.application.app.thirdservice.request.jd.JdExpressNotifyDTO;
import com.ets.delivery.application.app.thirdservice.response.jd.JdExpressNotifyVO;
import com.ets.delivery.application.common.consts.express.ExpressCodeEnum;
import com.ets.delivery.application.common.dto.express.ExpressNotifyDTO;
import com.ets.delivery.application.common.dto.pickUp.*;
import com.ets.delivery.application.common.vo.pickUp.*;
import com.ets.delivery.application.infra.entity.PickupLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;
import java.util.ArrayList;
import java.util.List;

@RestController
@RequestMapping("/admin/pickUpOrder")
@Slf4j
public class PickUpOrderController {

    @Autowired
    private PickUpBusiness pickUpBusiness;

    @Autowired
    private LogisticsBusiness logisticsBusiness;

    @Autowired
    private JdCloudExpressManage jdCloudExpressManage;

    @RequestMapping("/getList")
    public JsonResult<IPage<PickUpListVO>> getList(@RequestBody @Valid PickUpListDTO listDTO) {
        IPage<PickUpListVO> list = pickUpBusiness.getList(listDTO);
        return JsonResult.ok(list);
    }

    @RequestMapping("/getDetail")
    public JsonResult<PickUpDetailVO> getDetail(@RequestBody @Valid PickUpDetailDTO detailDTO) {
        PickUpDetailVO detail = pickUpBusiness.getDetail(detailDTO);
        return JsonResult.ok(detail);
    }

    @RequestMapping("/getBookingInfo")
    public JsonResult<PickUpOrderBookingInfoVO> getBookingInfo(@RequestBody @Valid PickUpOrderBookingInfoDTO infoDTO) {
        PickUpOrderBookingInfoVO bookingInfo = pickUpBusiness.getBookingInfo(infoDTO);
        return JsonResult.ok(bookingInfo);
    }
    
    @PostMapping("/getLogisticsInfo")
    public JsonResult<PickUpLogisticsInfoVO> getLogisticsInfo(@RequestBody @Valid PickUpLogisticsInfoDTO logisticsInfoDTO) {
        return JsonResult.ok(logisticsBusiness.getPickUpLogisticsInfo(logisticsInfoDTO.getPlateNo()));
    }

    @RequestMapping("/create")
    public JsonResult<?> create(@RequestBody @Valid PickUpOrderCreateDTO createDTO) {
        pickUpBusiness.createPickUpOrder(createDTO);
        return JsonResult.ok();
    }

    @RequestMapping("/cancel")
    public JsonResult<?> cancel(@RequestBody @Valid PickUpOrderCancelDTO cancelDTO) {
        pickUpBusiness.cancelPickUpOrder(cancelDTO);
        return JsonResult.ok();
    }

    @RequestMapping("/getLog")
    public JsonResult<IPage<PickUpLogListVO>> getLog(@RequestBody @Valid PickUpLogListDTO logListDTO) {
        IPage<PickUpLogListVO> list = pickUpBusiness.getLog(logListDTO);
        return JsonResult.ok(list);
    }

    @RequestMapping("/submitJdExpressNotify")
    public JsonResult<Object> submitJdExpressNotify(@RequestBody JdExpressNotifyDTO.RequestBody requestBody) {

        requestBody.setOperateTime(ToolsHelper.getDateTime());
        requestBody.setOperator("tester");
        if (StringUtils.isEmpty(requestBody.getTraceMark())) {
            requestBody.setTraceMark(requestBody.getTraceNode());
        }

        jdCloudExpressManage.expressNotifyByRequestBody(requestBody);

        return JsonResult.ok();
    }
}
