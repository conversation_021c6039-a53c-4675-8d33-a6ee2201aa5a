package com.ets.delivery.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.common.JsonResult;
import com.ets.delivery.application.app.business.SettingAfterSalesReviewBusiness;
import com.ets.delivery.application.common.dto.setting.*;
import com.ets.delivery.application.common.vo.setting.AfterSalesReviewReasonListVO;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 售后审核原因配置管理
 */
@RestController
@RequestMapping("/admin/setting/aftersales-review")
public class SettingAfterSalesReviewController {

    @Autowired
    private SettingAfterSalesReviewBusiness settingBusiness;

    /**
     * 获取售后审核原因列表
     * @param dto 查询参数
     * @return 分页列表数据
     */
    @PostMapping("/get-list")
    public JsonResult<IPage<AfterSalesReviewReasonListVO>> getList(@RequestBody @Valid AfterSalesReviewReasonListDTO dto) {
        return JsonResult.ok(settingBusiness.getAfterSalesReviewReasonList(dto));
    }

    /**
     * 添加售后审核原因
     * @param dto 添加参数
     * @return 添加结果
     */
    @PostMapping("/add")
    public JsonResult<Void> add(@RequestBody @Valid AfterSalesReviewReasonAddDTO dto) {
        settingBusiness.addAfterSalesReviewReason(dto);
        return JsonResult.ok();
    }

    /**
     * 编辑售后审核原因
     * @param dto 编辑参数
     * @return 编辑结果
     */
    @PostMapping("/edit")
    public JsonResult<Void> edit(@RequestBody @Valid AfterSalesReviewReasonEditDTO dto) {
        settingBusiness.editAfterSalesReviewReason(dto);
        return JsonResult.ok();
    }

}
