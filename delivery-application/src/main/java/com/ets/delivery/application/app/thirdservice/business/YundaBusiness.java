package com.ets.delivery.application.app.thirdservice.business;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.crypto.SecureUtil;
import com.alibaba.fastjson.JSON;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.thirdservice.feign.YundaApiFeign;
import com.ets.delivery.application.app.thirdservice.request.yunda.*;
import com.ets.delivery.application.app.thirdservice.response.yunda.*;
import com.ets.delivery.application.common.bo.yunda.YundaSignBO;
import com.ets.delivery.application.common.config.yunda.YundaConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Marshaller;
import jakarta.xml.bind.Unmarshaller;
import java.io.StringReader;
import java.io.StringWriter;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

@Slf4j
@Component
public class YundaBusiness {

    @Autowired
    private YundaConfig yundaConfig;

    @Autowired
    private YundaApiFeign yunDaApiFeign;

    public YundaDeliveryOrderCreateXmlVO deliveryOrderCreate(YundaDeliveryOrderCreateXmlDTO orderCreateDTO) {
        String method = "deliveryorder.create";

        // 发送请求
        String result = sendRequest(orderCreateDTO, method);
        return this.parseResponse(result, YundaDeliveryOrderCreateXmlVO.class);
    }

    public YundaOrderCancelXmlVO orderCancel(YundaOrderCancelXmlDTO orderCancelDTO) {
        String method = "order.cancel";

        // 发送请求
        String result = sendRequest(orderCancelDTO, method);
        return this.parseResponse(result, YundaOrderCancelXmlVO.class);
    }

    public YundaInventoryQueryXmlVO inventoryQuery(YundaInventoryQueryXmlDTO inventoryQueryDTO) {
        String method = "inventory.query";

        // 发送请求
        String result = sendRequest(inventoryQueryDTO, method);
        return this.parseResponse(result, YundaInventoryQueryXmlVO.class);
    }

    public YundaStockListVO stockList(YundaStockListDTO stockListDTO) {
        MultiValueMap<String, Object> body = new LinkedMultiValueMap<>();
        body.setAll(BeanUtil.beanToMap(stockListDTO));
        log.info("【stockList】请求参数：{}", body);
        String result = yunDaApiFeign.StockList(body);
        log.info("【stockList】返回结果：{}", result);
        return JSON.parseObject(result, YundaStockListVO.class);
    }

    // 入库单创建接口
    public StockResponseVO stockInCreate(StockInCreateXmlDTO dto) {

        String method = "entryorder.create";

        // 发送请求
        String result = sendRequest(dto, method);
        return this.parseResponse(result, StockResponseVO.class).checkError();
    }

    public StockResponseVO stockOutCreate(StockOutCreateXmlDTO dto) {

        String method = "stockout.create";

        // 发送请求
        String result = sendRequest(dto, method);
        return this.parseResponse(result, StockResponseVO.class).checkError();
    }

    public StockResponseVO stockCancel(StockCancelXmlDTO dto) {

        String method = "order.cancel";

        // 发送请求
        String result = sendRequest(dto, method);
        return this.parseResponse(result, StockResponseVO.class).checkError();
    }

    public DeliveryOrderQueryVO deliveryOrderQuery(DeliveryOrderQueryXmlDTO orderQueryXmlDTO) {
        String method = "zl.deliveryorder.query";

        // 发送请求
        String result = sendRequest(orderQueryXmlDTO, method);
        DeliveryOrderQueryXmlVO queryXmlVO = this.parseResponse(result, DeliveryOrderQueryXmlVO.class);
        DeliveryOrderQueryVO queryVO = new DeliveryOrderQueryVO();
        queryVO.setRawData(result);
        queryVO.setXmlVO(queryXmlVO);
        return queryVO;
    }


    /**
     * 生成签名
     *
     * @param method    方法名
     * @param timestamp 时间戳 yyyy-MM-dd HH:mm:ss
     * @return 签名 sign
     */
    private String getSign(String method, String timestamp, String body) {
        Map<String, String> params = new HashMap<>();
        params.put("method", method);
        params.put("timestamp", timestamp);
        params.put("format", "xml");
        params.put("app_key", yundaConfig.getAppKey());
        params.put("v", yundaConfig.getVersion());
        params.put("sign_method", yundaConfig.getSignMethod());
        params.put("customerId", yundaConfig.getCustomerId());
        Set<String> keySet = params.keySet();
        StringBuilder sb = new StringBuilder();
        keySet.stream().sorted().forEach((k) -> {
            if (params.get(k) != null && params.get(k).trim().length() > 0) {
                sb.append(k).append(params.get(k).trim());
            }
        });

        String signStr = yundaConfig.getSecret() + sb + body + yundaConfig.getSecret();
        log.info("签名字符串：{}", signStr);
        return SecureUtil.md5(signStr).toUpperCase(Locale.ROOT);
    }

    /**
     * 发送请求
     *
     * @param requestDTO 请求参数对象
     * @param method     方法名
     * @return 请求结果
     */
    private String sendRequest(Object requestDTO, String method) {
        try {
            // 转换xml格式
            StringWriter xmlStr = new StringWriter();
            JAXBContext context = JAXBContext.newInstance(requestDTO.getClass());
            Marshaller marshaller = context.createMarshaller();
            marshaller.setProperty(Marshaller.JAXB_FRAGMENT, true);
            marshaller.marshal(requestDTO, xmlStr);
            log.info("【{}】请求参数：{}", method, xmlStr);

            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            String sign = this.getSign(method, timestamp, xmlStr.toString());
            log.info("签名：{}", sign);

            // 发起请求
            String result = yunDaApiFeign.DeliveryOrder(
                    xmlStr.toString(),
                    yundaConfig.getAppKey(),
                    yundaConfig.getCustomerId(),
                    yundaConfig.getFormat(),
                    method,
                    yundaConfig.getSignMethod(),
                    timestamp,
                    yundaConfig.getVersion(),
                    sign,
                    yundaConfig.getChannel()
            );
            log.info("【{}】返回结果：{}", method, result);

            return result;
        } catch (JAXBException e) {
            log.error("转换xml错误：{}", e.getMessage());
            ToolsHelper.throwException("转换xml格式出错");
        }
        return null;
    }

    /**
     * 解析xml到对象
     *
     * @param xml  返回内容
     * @param type 类
     * @return 类
     */
    public <T> T parseResponse(String xml, Class<T> type) {
        try {
            JAXBContext jaxbContext = JAXBContext.newInstance(type);
            Unmarshaller jaxbUnmarshaller = jaxbContext.createUnmarshaller();
            return (T) jaxbUnmarshaller.unmarshal(new StringReader(xml));
        } catch (JAXBException e) {
            log.error("xml转换对象错误：{}", e.getMessage());
            ToolsHelper.throwException("解析xml格式出错");
        }
        return null;
    }

    public void checkSign(YundaSignBO signBO) {
        Map<String, String> params = new HashMap<>();
        params.put("method", signBO.getMethod());
        params.put("timestamp", signBO.getTimestamp());
        params.put("format", signBO.getFormat());
        params.put("app_key", signBO.getAppKey());
        params.put("v", signBO.getV());
        params.put("sign_method", signBO.getSignMethod());
        params.put("customerId", signBO.getCustomerId());
        Set<String> keySet = params.keySet();
        StringBuilder sb = new StringBuilder();
        keySet.stream().sorted().forEach((k) -> {
            if (params.get(k) != null && params.get(k).trim().length() > 0) {
                sb.append(k).append(params.get(k).trim());
            }
        });

        String signStr = yundaConfig.getSecret() + sb + signBO.getBody().replaceAll("[\t\r\n]", "") + yundaConfig.getSecret();
        log.info("签名字符串：{}", signStr);
        String genSign = SecureUtil.md5(signStr).toUpperCase(Locale.ROOT);

        if (!genSign.equals(signBO.getSign())) {
            log.error("签名校验失败，生成签名：{}，传入签名：{}", genSign, signBO.getSign());
            if (yundaConfig.getCheckSign()) {
                ToolsHelper.throwException("签名校验失败");
            }
        }
    }
}
