package com.ets.delivery.application.app.factory.serial;

import com.ets.delivery.application.common.bo.serial.SerialProcessBO;

import java.util.List;

/**
 * 序列号处理器接口
 * 定义序列号处理的统一规范
 */
public interface ISerial {
    
    /**
     * 处理序列号
     * @param processBO 序列号处理业务对象
     */
    void process(SerialProcessBO processBO);

    /**
     * 根据业务单号获取序列号列表
     * @param businessSn 业务单号
     * @return 序列号列表
     */
    List<String> getSerialListByBusinessSn(String businessSn);

    /**
     * 删除业务相关的序列号记录
     * @param businessSn 业务单号
     */
    void deleteSerialBusinessRecord(String businessSn);
}