package com.ets.delivery.application.app.factory.storage.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.ets.delivery.application.app.factory.storage.IStorage;
import com.ets.delivery.application.common.bo.exwarehouse.ExWarehouseSplitDetailBO;
import com.ets.delivery.application.common.dto.logistics.LogisticsAddOrderDTO;
import com.ets.delivery.application.common.dto.logistics.LogisticsCancelOrderDTO;
import com.ets.delivery.application.common.dto.logistics.LogisticsOrderConfirmDTO;
import com.ets.delivery.application.common.dto.logistics.LogisticsOrderProcessDTO;
import com.ets.delivery.application.common.vo.InventoryQueryVO;
import com.ets.delivery.application.common.vo.WarehouseStockListVO;
import com.ets.delivery.application.common.vo.alarm.StorageStockAlarmFileVO;
import com.ets.delivery.application.infra.entity.ExWarehouse;
import com.ets.delivery.application.infra.entity.ExWarehouseDetail;
import com.ets.delivery.application.infra.entity.ExWarehouseSplitDetail;
import com.ets.delivery.application.infra.entity.SupplyGoods;
import com.ets.delivery.application.infra.service.ExWarehouseDetailService;
import com.ets.delivery.application.infra.service.ExWarehouseService;
import com.ets.delivery.application.infra.service.ExWarehouseSplitDetailService;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

public abstract class StorageBase implements IStorage {

    @Autowired
    ExWarehouseService exWarehouseService;

    @Autowired
    ExWarehouseDetailService exWarehouseDetailService;

    @Autowired
    ExWarehouseSplitDetailService exWarehouseSplitDetailService;

    @Override
    public ExWarehouse initExWarehouse(LogisticsAddOrderDTO addOrderDTO) {
        ExWarehouse exWarehouse = exWarehouseService.getOneByIsvUuid(addOrderDTO.getLogisticsSn());
        if (ObjectUtil.isNull(exWarehouse)) {
            // 创建出库单
            exWarehouse = new ExWarehouse();
            exWarehouse.setStorageCode(addOrderDTO.getStorageCode());
            exWarehouse.setIsvUuid(addOrderDTO.getLogisticsSn());
            exWarehouse.setConsigneeName(addOrderDTO.getName());
            exWarehouse.setConsigneeMobile(addOrderDTO.getMobile());
            exWarehouse.setConsigneeProvince(addOrderDTO.getProvince());
            exWarehouse.setConsigneeCity(addOrderDTO.getCity());
            exWarehouse.setConsigneeArea(addOrderDTO.getArea());
            exWarehouse.setConsigneeAddress(addOrderDTO.getDetailAddress());
            exWarehouse.setLogisticsCode(addOrderDTO.getLogisticsCode());
            exWarehouse.setRemark(addOrderDTO.getRemark());
            exWarehouse.setCreatedAt(LocalDateTime.now());
            exWarehouse.setUpdatedAt(LocalDateTime.now());
            exWarehouseService.save(exWarehouse);
        }
        return exWarehouse;
    }

    @Override
    public List<ExWarehouseDetail> initExWarehouseDetail(LogisticsAddOrderDTO addOrderDTO) {
        List<ExWarehouseDetail> list = new ArrayList<>();
        // 创建出库单商品详情
        addOrderDTO.getGoodsList().forEach(goods -> {
            ExWarehouseDetail exWarehouseDetail = exWarehouseDetailService.getOneByIsvUuidAndGoodsNo(
                    addOrderDTO.getLogisticsSn(), goods.getGoodsCode());

            if (ObjectUtil.isNull(exWarehouseDetail)) {
                exWarehouseDetail = new ExWarehouseDetail();
                exWarehouseDetail.setIsvUuid(addOrderDTO.getLogisticsSn());
                exWarehouseDetail.setGoodsNo(goods.getGoodsCode());
                exWarehouseDetail.setQuantity(goods.getQuantity());
                exWarehouseDetail.setCreatedAt(LocalDateTime.now());
                exWarehouseDetail.setUpdatedAt(LocalDateTime.now());
                exWarehouseDetailService.save(exWarehouseDetail);
            } else if(!exWarehouseDetail.getQuantity().equals(goods.getQuantity())) {
                exWarehouseDetail.setQuantity(goods.getQuantity());
                exWarehouseDetailService.updateById(exWarehouseDetail);
            }
            list.add(exWarehouseDetail);
        });
        return list;
    }

    public ExWarehouse initExWarehouseSplit(ExWarehouse exWarehouse, String splitOrderNo) {
        ExWarehouse splitExWarehouse = exWarehouseService.getOneBySplitOrderNo(splitOrderNo);
        if (ObjectUtils.isEmpty(splitExWarehouse)) {
            // 初始化子单出库单
            splitExWarehouse = new ExWarehouse();
            splitExWarehouse.setIsvUuid(exWarehouse.getIsvUuid());
            splitExWarehouse.setDeliverOrderNo(exWarehouse.getDeliverOrderNo());
            splitExWarehouse.setSplitOrderNo(splitOrderNo);
            splitExWarehouse.setConsigneeName(exWarehouse.getConsigneeName());
            splitExWarehouse.setConsigneeMobile(exWarehouse.getConsigneeMobile());
            splitExWarehouse.setConsigneeProvince(exWarehouse.getConsigneeProvince());
            splitExWarehouse.setConsigneeCity(exWarehouse.getConsigneeCity());
            splitExWarehouse.setConsigneeArea(exWarehouse.getConsigneeArea());
            splitExWarehouse.setConsigneeAddress(exWarehouse.getConsigneeAddress());
            splitExWarehouse.setStorageCode(exWarehouse.getStorageCode());
            splitExWarehouse.setSplitFlag(0);
            splitExWarehouse.setCreatedAt(LocalDateTime.now());
            splitExWarehouse.setUpdatedAt(LocalDateTime.now());
            exWarehouseService.save(splitExWarehouse);
        }
        return splitExWarehouse;
    }

    public void initExWarehouseSplitDetail(ExWarehouse exWarehouse, String splitOrderNo, List<ExWarehouseSplitDetailBO> orderDetailList) {
        // 初始化子单出库单明细
        orderDetailList.forEach(orderDetail -> {
            ExWarehouseSplitDetail splitDetail = exWarehouseSplitDetailService.getOneBySplitOrderNoAndGoodsNo(splitOrderNo, orderDetail.getGoodsNo());
            if (ObjectUtils.isEmpty(splitDetail)) {
                splitDetail = BeanUtil.copyProperties(orderDetail, ExWarehouseSplitDetail.class);
                splitDetail.setDeliverOrderNo(exWarehouse.getDeliverOrderNo());
                splitDetail.setSplitOrderNo(splitOrderNo);
                splitDetail.setCreatedAt(LocalDateTime.now());
                splitDetail.setUpdatedAt(LocalDateTime.now());
                exWarehouseSplitDetailService.save(splitDetail);
            }
        });
    }

    @Override
    public String addOrder(ExWarehouse exWarehouse, List<ExWarehouseDetail> detailList) {
        return "";
    }

    @Override
    public void cancelOrder(LogisticsCancelOrderDTO cancelOrderDTO) {

    }

    @Override
    public void orderConfirm(LogisticsOrderConfirmDTO confirmDTO) {

    }

    @Override
    public void erpOrderConfirm(LogisticsOrderConfirmDTO confirmDTO) {

    }

    @Override
    public ExWarehouse orderQuery(ExWarehouse exWarehouse) {
        return null;
    }

    @Override
    public List<InventoryQueryVO> inventoryQuery(List<String> goodsCodeList) {
        return null;
    }

    @Override
    public void orderProcess(LogisticsOrderProcessDTO orderProcessDTO) {

    }

    @Override
    public List<WarehouseStockListVO> stockList(String startDate, String endDate) {
        return null;
    }

    public StorageStockAlarmFileVO stockAlarm(List<SupplyGoods> goodsList) {
        return null;
    }
}
