package com.ets.delivery.application.common.consts.yunda;

import lombok.Getter;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Getter
public enum YundaInventoryTypeEnum {

    ZP("ZP", "正品"),
    CC("CC", "残次"),
    JS("JS", "机损"),
    XS("XS", " 箱损"),
    ZT("ZT", "在途库存");

    private final String value;
    private final String desc;
    public static final Map<String, String> map;
    
    YundaInventoryTypeEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }
    
    static {
        YundaInventoryTypeEnum[] enums = YundaInventoryTypeEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
    
    public static boolean isValidValue(String value) {
        if (value == null) {
            return false;
        }
        return Arrays.stream(YundaInventoryTypeEnum.values())
                .anyMatch(enumValue -> enumValue.getValue().equals(value));
    }

    public static List<Map<String, String>> getSelectOptions() {
        return Arrays.stream(YundaInventoryTypeEnum.values())
                .map(enumValue -> Map.of("label", enumValue.getDesc(), "value", enumValue.getValue()))
                .collect(Collectors.toList());
    }

    public static String getDescByValue(String value) {
        return map.getOrDefault(value, "未知");
    }
}
