package com.ets.delivery.application.controller.nologin;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.business.TaskRecordBusiness;
import com.ets.delivery.application.common.dto.task.TaskExecDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/no-login/task")
public class TaskController {

    @Autowired
    private TaskRecordBusiness taskRecordBusiness;

    @RequestMapping("/exec")
    public JsonResult<?> exec(@RequestBody @Valid TaskExecDTO execDTO) {
        taskRecordBusiness.findAndExec(execDTO);
        return JsonResult.ok();
    }
}
