package com.ets.delivery.application.controller.testing;

import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONArray;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.factory.task.TaskFactory;
import com.ets.delivery.application.app.factory.task.impl.TaskLogisticsDeliverGoods;
import com.ets.delivery.application.common.config.queue.task.QueueTask;
import com.ets.delivery.application.common.consts.logistics.LogisticsDeliveryStatusEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsOperateTypeEnum;
import com.ets.delivery.application.common.consts.logistics.LogisticsStatusEnum;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.delivery.application.common.dto.logistics.LogisticsAcceptDTO;
import com.ets.delivery.application.common.dto.task.LogisticsConfirmDTO;
import com.ets.delivery.application.common.dto.task.TaskRecordDTO;
import com.ets.delivery.application.infra.entity.Logistics;
import com.ets.delivery.application.infra.entity.TaskRecord;
import com.ets.delivery.application.infra.service.LogisticsLogService;
import com.ets.delivery.application.infra.service.LogisticsService;
import com.ets.delivery.application.infra.service.TaskRecordService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;

@Slf4j
@RestController
@RequestMapping("/no-login/hzj")
public class Hzjcontroller {
    @Autowired
    private TaskRecordService taskRecordService;
    @Autowired
    private QueueTask queueTask;
    @Autowired
    private LogisticsService logisticsService;
    @Autowired
    private LogisticsLogService logisticsLogService;

    @Qualifier("redisPermanentTemplate")
    @Autowired
    private StringRedisTemplate redisTemplate;

    @Value("${spring.profiles.active}")
    private String ACTIVE;


    @RequestMapping("/test")
    public JsonResult<Boolean> test(@RequestParam(value = "taskSn", required = false) String taskSn ) {

        //推送到队列
        //queueTask.push(new TaskDisposer(taskRecord));
        SpringUtil.getBean(TaskLogisticsDeliverGoods.class).execute(taskSn);

        return JsonResult.ok(true);
    }

    /*
     *  发货通知业务方
     */
    @RequestMapping("/shipNotify")
    public JsonResult<Boolean> shipNotify(@RequestBody @Validated LogisticsConfirmDTO logisticsConfirmDTO ) {
        //更新发货单
        Logistics logistics = logisticsService.getByLogisticsSn(logisticsConfirmDTO.getLogisticsSn());
        logistics.setExpressCorp(logisticsConfirmDTO.getExpressCorp());
        logistics.setExpressNumber(logisticsConfirmDTO.getExpressNumber());
        logistics.setDeliveryStatus(LogisticsDeliveryStatusEnum.DELIVERY_STATUS_SHIPPED.getValue());
        logistics.setDeliveryTime(LocalDateTime.parse(logisticsConfirmDTO.getDeliveryTime(),DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        logistics.setUpdatedAt(LocalDateTime.now());
        //logistics.update()
        logisticsService.save(logistics);
        //添加操作记录

        //添加日志
        String msg = "物流单号："+logisticsConfirmDTO.getExpressNumber();
        logisticsLogService.addLog(logistics.getId(), LogisticsOperateTypeEnum.TYPE_MODIFY.getCode(),msg,"system");
        Map<String,Integer> map = new HashMap<>();
        map.put("logistics_id",logistics.getId());
        //通知发货
        //todo 塞队列进行发货操作
        TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
        taskRecordDTO.setTaskSn(ToolsHelper.genNum(redisTemplate,"task_record",ACTIVE,8));
        taskRecordDTO.setReferSn(logistics.getLogisticsSn());
        taskRecordDTO.setReferType(TaskRecordReferTypeEnum.TASK_LOGISTICS_SHIP.getType());
        taskRecordDTO.setNextExecTime(LocalDateTime.now());
        taskRecordDTO.setNotifyContent(JSONArray.toJSONString(map));
        TaskFactory.create(TaskRecordReferTypeEnum.TASK_LOGISTICS_SHIP.getType()).addAndPush(taskRecordDTO);

        return JsonResult.ok(true);
    }
}
