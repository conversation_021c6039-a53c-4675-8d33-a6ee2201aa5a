package com.ets.delivery.application.common.consts.manualLogistics;

import lombok.Getter;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.stream.IntStream;

@Getter
public enum ManualLogisticsDeliveryStatusEnum {
    DELIVERY_STATUS_WAIT(0, "已保存"),
    DELIVERY_STATUS_PROCESSING(1, "已推单"),
    DELIVERY_STATUS_SHIPPED(2, "已完成"),
    DELIVERY_STATUS_CANCEL(3, "已取消"),
    DELIVERY_STATUS_STOP(4, "发货暂停");

    private final Integer value;
    private final String desc;
    public static final Map<Integer, String> map;

    ManualLogisticsDeliveryStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    static {
        ManualLogisticsDeliveryStatusEnum[] enums = ManualLogisticsDeliveryStatusEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
    }
}
