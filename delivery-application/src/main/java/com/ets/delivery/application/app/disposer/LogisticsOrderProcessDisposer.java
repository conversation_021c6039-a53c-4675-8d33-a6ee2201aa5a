package com.ets.delivery.application.app.disposer;

import com.ets.common.queue.BaseDisposer;
import com.ets.delivery.application.app.factory.storage.StorageFactory;
import com.ets.delivery.application.common.dto.logistics.LogisticsOrderProcessDTO;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@NoArgsConstructor
@Component(value = "LogisticsOrderProcessJobBean")
public class LogisticsOrderProcessDisposer extends BaseDisposer {

    public LogisticsOrderProcessDisposer(Object params) {
        super(params);
    }

    @Override
    public String getJobBeanName() {
        return "LogisticsOrderProcessJobBean";
    }

    @Override
    public void execute(Object content) throws Exception {
        LogisticsOrderProcessDTO processDTO = super.getParamsObject(content, LogisticsOrderProcessDTO.class);

        try {
            StorageFactory.create(processDTO.getStorageCode()).orderProcess(processDTO);
        } catch (Throwable e) {
            log.error("【订单流水通知】{}", e.getLocalizedMessage());
        }
    }
}
