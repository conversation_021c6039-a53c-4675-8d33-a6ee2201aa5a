package com.ets.delivery.application.common.consts.storage;

import com.ets.delivery.application.app.factory.storage.impl.JSStorageManage;
import com.ets.delivery.application.app.factory.storage.impl.JdCloudStorageManage;
import com.ets.delivery.application.app.factory.storage.impl.NormalStorageManage;
import com.ets.delivery.application.app.factory.storage.impl.StorageBase;
import com.ets.delivery.application.app.factory.storage.impl.YundaStorageManage;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

@Getter
@AllArgsConstructor
public enum StorageCodeEnum {

    YUNDA(StorageCodeConstant.YUNDA, YundaStorageManage.class, "韵达"),
    JD_CLOUD(StorageCodeConstant.JD_CLOUD, JdCloudStorageManage.class, "京东云仓"),
    NORMAL(StorageCodeConstant.NORMAL, NormalStorageManage.class, "Normal"),
    AITESI("aitesi", null, "埃特斯"),
    JS_HUANHUO("JShuanhuo", JSStorageManage.class, "江苏仓");

    private final String value;
    private final Class<? extends StorageBase> job;
    private final String desc;
    public static final Map<String, String> map;
    public static final List<String> list;

    static {
        StorageCodeEnum[] enums = StorageCodeEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
        list = Stream.of(enums)
                .map(StorageCodeEnum::getValue)
                .collect(Collectors.toList());
    }

    public static StorageCodeEnum getByType(String storageCode) {
        for (StorageCodeEnum storageCodeEnum : StorageCodeEnum.values()) {
            if (storageCodeEnum.getValue().equals(storageCode)) {
                return storageCodeEnum;
            }
        }
        return null;
    }

    public static boolean isValidValue(String storageCode) {
        return list.contains(storageCode);
    }

    public static List<Map<String, String>> getSelectOptions() {
        return Arrays.stream(StorageCodeEnum.values())
                .map(enumValue -> Map.of("label", enumValue.getDesc(), "value", enumValue.getValue()))
                .collect(Collectors.toList());
    }
}
