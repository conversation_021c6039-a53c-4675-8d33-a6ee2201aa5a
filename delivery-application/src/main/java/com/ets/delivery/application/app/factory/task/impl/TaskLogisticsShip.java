package com.ets.delivery.application.app.factory.task.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ets.common.BizException;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.app.business.NotifyBusiness;
import com.ets.delivery.application.app.factory.task.TaskFactory;
import com.ets.delivery.application.app.thirdservice.feign.NotifyFeign;
import com.ets.delivery.application.common.bo.notify.LogisticsShipNotifyBO;
import com.ets.delivery.application.common.bo.task.TaskExpressSubscribeBO;
import com.ets.delivery.application.common.consts.express.ExpressSubscribeOrderTypeEnum;
import com.ets.delivery.application.common.consts.logistics.*;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordErrorCodeConstant;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.delivery.application.common.dto.task.TaskRecordDTO;
import com.ets.delivery.application.infra.entity.Logistics;
import com.ets.delivery.application.infra.entity.ReDelivery;
import com.ets.delivery.application.infra.entity.TaskRecord;
import com.ets.delivery.application.infra.service.LogisticsLogService;
import com.ets.delivery.application.infra.service.LogisticsService;
import com.ets.delivery.application.infra.service.ReDeliveryService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.net.URI;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.HashMap;

@Slf4j
@Component
public class TaskLogisticsShip extends TaskBase {

    @Autowired
    private LogisticsService logisticsService;

    @Autowired
    private LogisticsLogService logisticsLogService;

    @Autowired
    private ReDeliveryService reDeliveryService;

    @Autowired
    private NotifyBusiness notifyBusiness;

    @Autowired
    private NotifyFeign notifyFeign;

    @Override
    public void childExec(TaskRecord taskRecord) {

        // 查询发货单
        Logistics logistics = logisticsService.getByLogisticsSn(taskRecord.getReferSn());
        if (ObjectUtils.isEmpty(logistics) || logistics.getStatus().equals(LogisticsStatusEnum.STATUS_CANCEL.getValue())) {
            String msg = "发货单：" + taskRecord.getReferSn() + "不存在或已取消";
            // 记录日志
            logisticsLogService.addLog(logistics.getId(),
                    LogisticsOperateTypeEnum.TYPE_SHIP.getCode(),
                    msg,
                    "system");
            ToolsHelper.throwException(msg, TaskRecordErrorCodeConstant.ERROR_CODE_NEED_FINISH);
        }

        // 地推发货通知
        if (logistics.getOrderSource().equals(LogisticsOrderSourceEnum.RE_DELIVERY.getValue())) {
            offlineShipSuccess(logistics);
        }

        // 手动下单订单不通知
        if (!logistics.getOrderType().equals(LogisticsOrderTypeEnum.MANUAL.getValue())) {
            // 通知业务
            String signType = "MD5";
            if (Arrays.asList(
                    LogisticsOrderTypeEnum.AFTER_SALES_MAINTAIN_EXCHANGE.getValue(),
                    LogisticsOrderTypeEnum.AFTER_SALES_MAINTAIN_EXCHANGE_APPLY.getValue(),
                    LogisticsOrderTypeEnum.AFTER_SALES_EXCHANGE.getValue(),
                    LogisticsOrderTypeEnum.AFTER_SALES_EXCHANGE_APPLY.getValue(),
                    LogisticsOrderTypeEnum.AFTER_SALES_RECALL_EXCHANGE.getValue(),
                    LogisticsOrderTypeEnum.REAPPLY.getValue(),
                    LogisticsOrderTypeEnum.REISSUE_APPLY.getValue(),
                    LogisticsOrderTypeEnum.REISSUE_MAINTAIN_EXCHANGE.getValue(),
                    LogisticsOrderTypeEnum.REISSUE_REAPPLY.getValue(),
                    LogisticsOrderTypeEnum.UPGRADE.getValue()
            ).contains(logistics.getOrderType())) {
                signType = "custom";
            }

            LogisticsShipNotifyBO notifyBO = new LogisticsShipNotifyBO();
            notifyBO.setOrder_sn(logistics.getOrderSn());
            notifyBO.setStatus(1);
            notifyBO.setExpress_corp(logistics.getExpressCorp());
            notifyBO.setExpress_number(logistics.getExpressNumber());
            notifyBO.setExpress_time(ToolsHelper.localDateTimeToString(logistics.getDeliveryTime()));
            notifyBO.setRemark(logistics.getDeliveryRemark());

            HashMap<String, String> headers = notifyBusiness.getNotifyHeaders(logistics.getAppId(), notifyBO, signType);

            try {
                String result = notifyFeign.logisticsShipNotify(URI.create(logistics.getNotifyBackUrl()), notifyBO, headers);
                if (StringUtils.isEmpty(result)) {
                    ToolsHelper.throwException("通知返回结果为空");
                }
                JsonResult<Object> jsonResult = JsonResult.convertFromJsonStr(result, Object.class);
                jsonResult.checkError();
            } catch (BizException e) {
                switch (e.getErrorCode()) {
                    //201218 已签收的订单调发货接口返货这个错误码
                    case 201218:
                        shipSuccess(logistics, e.getErrorMsg());
                        break;
                    //订单已取消,错误数据
                    case 201217:
                        // 更新状态为取消
                        String errorMsg = e.getErrorMsg().length() > 200 ? StringUtils.substring(e.getErrorMsg(), 0, 200) : e.getErrorMsg();
                        LambdaUpdateWrapper<Logistics> updateWrapper = new LambdaUpdateWrapper<>();
                        updateWrapper.eq(Logistics::getId, logistics.getId());
                        updateWrapper.set(Logistics::getStatus, LogisticsStatusEnum.STATUS_CANCEL.getValue());
                        updateWrapper.set(Logistics::getDeliveryRemark, errorMsg);
                        updateWrapper.set(Logistics::getUpdatedAt, LocalDateTime.now());
                        logisticsService.update(updateWrapper);

                        shipSuccess(logistics, e.getErrorMsg());
                        break;
                    default:
                        // 通知失败
                        shipFail(logistics, "失败原因：" + e.getErrorMsg());
                        ToolsHelper.throwException("失败原因：" + e.getErrorMsg());
                }
            } catch (Throwable e) {
                // 通知失败
                shipFail(logistics, "失败原因：" + e.getMessage());
                ToolsHelper.throwException("失败原因：" + e.getMessage());
            }
        }

        // 通知成功
        shipSuccess(logistics, "发货成功");

    }

    private void shipSuccess(Logistics logistics, String msg) {
        String errorMsg = msg.length() > 200 ? StringUtils.substring(msg, 0, 200) : msg;

        LambdaUpdateWrapper<Logistics> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Logistics::getId, logistics.getId());
        updateWrapper.set(Logistics::getNotifyTime, LocalDateTime.now());
        updateWrapper.set(Logistics::getNotifyStatus, LogisticsNotifyStatusEnum.NOTIFY_STATUS_SUCCESS.getValue());
        updateWrapper.set(Logistics::getNotifyRemark, errorMsg);
        updateWrapper.set(Logistics::getUpdatedAt, LocalDateTime.now());
        logisticsService.update(updateWrapper);

        // 操作日志
        logisticsLogService.addLog(logistics.getId(),
                LogisticsOperateTypeEnum.TYPE_SHIP.getCode(),
                "发货成功：" + logistics.getLogisticsSn() + "，原因：" + errorMsg,
                "system");

        // 订阅快递物流
        TaskExpressSubscribeBO subscribeBO = new TaskExpressSubscribeBO();
        subscribeBO.setOrderType(ExpressSubscribeOrderTypeEnum.ORDER_TYPE_LOGISTICS.getValue());
        subscribeBO.setOrderSn(logistics.getLogisticsSn());
        subscribeBO.setExpressNumber(logistics.getExpressNumber());
        String content = JSON.toJSONString(subscribeBO);

        TaskRecordDTO taskRecordDTO = new TaskRecordDTO();
        taskRecordDTO.setReferSn(logistics.getLogisticsSn());
        taskRecordDTO.setReferType(TaskRecordReferTypeEnum.TASK_EXPRESS_SUBSCRIBE.getType());
        taskRecordDTO.setNotifyContent(content);
        TaskFactory.create(TaskRecordReferTypeEnum.TASK_EXPRESS_SUBSCRIBE).addAndPush(taskRecordDTO);
    }

    private void shipFail(Logistics logistics, String msg) {
        String errorMsg = msg.length() > 200 ? StringUtils.substring(msg, 0, 200) : msg;

        LambdaUpdateWrapper<Logistics> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(Logistics::getId, logistics.getId());
        updateWrapper.set(Logistics::getNotifyTime, LocalDateTime.now());
        updateWrapper.set(Logistics::getNotifyStatus, LogisticsNotifyStatusEnum.NOTIFY_STATUS_FAIL.getValue());
        updateWrapper.set(Logistics::getNotifyRemark, errorMsg);
        updateWrapper.set(Logistics::getUpdatedAt, LocalDateTime.now());
        logisticsService.update(updateWrapper);

        // 操作日志
        logisticsLogService.addLog(logistics.getId(),
                LogisticsOperateTypeEnum.TYPE_SHIP.getCode(),
                "发货失败：" + logistics.getLogisticsSn() + "，原因：" + errorMsg,
                "system");
    }

    private void offlineShipSuccess(Logistics logistics) {
        ReDelivery reDelivery = reDeliveryService.getByDeliverySn(logistics.getOrderSn());
        if (reDelivery == null) {
            ToolsHelper.throwException("找不到对应地推发货单");
        }

        LambdaUpdateWrapper<ReDelivery> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ReDelivery::getId, reDelivery.getId());
        updateWrapper.set(ReDelivery::getExpressCorp, logistics.getExpressCorp());
        updateWrapper.set(ReDelivery::getExpressNumber, logistics.getExpressNumber());
        updateWrapper.set(ReDelivery::getDeliveryTime, logistics.getDeliveryTime());
        updateWrapper.set(ReDelivery::getStatus, 2);
        updateWrapper.set(ReDelivery::getUpdatedAt, LocalDateTime.now());
        reDeliveryService.update(updateWrapper);
    }
}
