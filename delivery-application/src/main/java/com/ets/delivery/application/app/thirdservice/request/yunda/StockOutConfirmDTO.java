package com.ets.delivery.application.app.thirdservice.request.yunda;

import lombok.Data;

import jakarta.xml.bind.annotation.*;
import java.math.BigDecimal;
import java.util.List;

@Data
@XmlRootElement(name = "request")
@XmlAccessorType(XmlAccessType.FIELD)
public class StockOutConfirmDTO {

    @XmlElement(name = "deliveryOrder")
    private DeliveryOrder deliveryOrder;

    @XmlElementWrapper(name = "orderLines")
    @XmlElement(name = "orderLine")
    private List<OrderLine> orderLines;

    @XmlElementWrapper(name = "packages")
    @XmlElement(name = "package")
    private List<DeliveryPackage> packageList;

    @Data
    public static class DeliveryOrder {
        Integer totalOrderLines;
        String deliveryOrderCode;
        String deliveryOrderId;
        String warehouseCode;
        String orderType;
        String status;
        String outBizCode;
        Integer confirmType;
        String logisticsCode;
        String logisticsName;
        String expressCode;
        String orderConfirmTime;
    }

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class DeliveryPackage {
        String logisticsName;
        String expressCode;
        String packageCode;
        BigDecimal length;
        BigDecimal width;
        BigDecimal height;
        BigDecimal weight;
        BigDecimal volume;

        @XmlElementWrapper(name = "packageMaterialList")
        @XmlElement(name = "packageMaterial")
        List<PackageMaterial> packageMaterialList;

        @XmlElementWrapper(name = "items")
        @XmlElement(name = "item")
        List<Item> item;

        @Data
        public static class PackageMaterial {
            String type;
            Integer quantity;
        }

        @Data
        public static class Item {
            String itemCode;
            String itemId;
            Integer quantity;
        }
    }

    @Data
    @XmlAccessorType(XmlAccessType.FIELD)
    public static class OrderLine {
        String outBizCode;
        String orderLineNo;
        String itemCode;
        String itemId;
        String itemName;
        String inventoryType;
        Integer actualQty;
        String batchCode;
        String productDate;
        String expireDate;
        String produceCode;

        @XmlElementWrapper(name = "batchs")
        @XmlElement(name = "batch")
        List<Batch> batchs;

        @Data
        public static class Batch {
            String batchCode;
            String productDate;
            String expireDate;
            String produceCode;
            String inventoryType;
            Integer actualQty;
        }

        @XmlElementWrapper(name = "snList")
        @XmlElement(name = "sn")
        List<String> sn;
    }

}
