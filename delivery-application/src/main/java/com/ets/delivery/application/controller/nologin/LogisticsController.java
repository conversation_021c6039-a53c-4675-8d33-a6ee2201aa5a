package com.ets.delivery.application.controller.nologin;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.business.LogisticsBusiness;
import com.ets.delivery.application.common.dto.logistics.*;
import com.ets.delivery.application.common.vo.LogisticsAddOrderVO;
import com.ets.delivery.application.common.vo.logistics.AcceptVO;
import com.ets.delivery.application.common.vo.logistics.AttemptCancelVO;
import com.ets.delivery.application.common.vo.logistics.LogisticsSkuSummaryVO;
import com.ets.delivery.feign.request.logistics.LogisticsCancelByOrderSnDTO;
import com.ets.delivery.feign.request.logistics.LogisticsFindByOrderSnDTO;
import com.ets.delivery.feign.response.logistics.LogisticsVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/no-login/logistics")
public class LogisticsController {

    @Autowired
    private LogisticsBusiness logisticsBusiness;

    /*
     *  创建出库单
     */
    @RequestMapping("/addOrder")
    public JsonResult<LogisticsAddOrderVO> addOrder(@RequestBody @Valid LogisticsAddOrderDTO addOrderDTO) {
        LogisticsAddOrderVO addOrderVO = logisticsBusiness.deliverOrder(addOrderDTO);
        return JsonResult.ok(addOrderVO);
    }

    @RequestMapping("/cancelOrder")
    public JsonResult<?> cancelOrder(@RequestBody @Valid LogisticsCancelOrderDTO cancelOrderDTO) {
        logisticsBusiness.cancelOrder(cancelOrderDTO);
        return JsonResult.ok();
    }

    /*
     *  新商品订单使用， 新增发货单
     */
    @RequestMapping("/accept")
    public JsonResult<AcceptVO> accept(@RequestBody @Validated LogisticsAcceptDTO logisticsAcceptDTO) {
        return JsonResult.ok(logisticsBusiness.accept(logisticsAcceptDTO));
    }

    /*
     *  新商品订单使用  尝试取消发货单
     */
    @RequestMapping("/attemptCancel")
    public JsonResult<AttemptCancelVO> attemptCancel(@RequestBody @Validated LogisticsAttemptCancelDTO logisticsAttemptCancelDTO) {
        return JsonResult.ok(logisticsBusiness.attemptCancel(logisticsAttemptCancelDTO.getLogisticsSn()));
    }

    @RequestMapping("/cancelByOrderSn")
    public JsonResult<?> cancelByOrderSn(@RequestBody @Valid LogisticsCancelByOrderSnDTO cancelByOrderSnDTO) {
        logisticsBusiness.cancelByOrderSn(cancelByOrderSnDTO);
        return JsonResult.ok();
    }

    /**
     * 通过订单号查询发货单信息
     */
    @RequestMapping("/findByOrderSn")
    public JsonResult<LogisticsVO> findByOrderSn(@RequestBody @Valid LogisticsFindByOrderSnDTO findByOrderSnDTO) {
        return JsonResult.ok(logisticsBusiness.findByOrderSn(findByOrderSnDTO));
    }

    /**
     * 通过快递单号查询发货单的SKU数量总额
     *
     * @param dto 查询请求参数
     * @return SKU数量总额信息
     */
    @PostMapping("/get-sku-summary-by-express-number")
    public JsonResult<LogisticsSkuSummaryVO> getSkuSummaryByExpressNumber(@RequestBody @Valid LogisticsSkuSummaryByExpressDTO dto) {
        LogisticsSkuSummaryVO summaryVO = logisticsBusiness.getSkuSummaryByExpressNumber(dto.getExpressNumber());
        return JsonResult.ok(summaryVO);
    }
}
