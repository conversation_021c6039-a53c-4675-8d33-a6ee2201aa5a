package com.ets.delivery.application.controller.notify;


import com.ets.delivery.application.app.business.ExpressBusiness;
import com.ets.delivery.application.app.business.LogisticsBusiness;
import com.ets.delivery.application.app.business.stock.StockNotifyBusiness;
import com.ets.delivery.application.app.thirdservice.business.YundaBusiness;
import com.ets.delivery.application.app.thirdservice.request.yunda.*;
import com.ets.delivery.application.app.thirdservice.request.yundaOpen.YundaOpenExpressNotifyDTO;
import com.ets.delivery.application.app.thirdservice.response.yunda.DeliveryConfirmVO;
import com.ets.delivery.application.app.thirdservice.response.yunda.StockResponseVO;
import com.ets.delivery.application.app.thirdservice.response.yunda.YundaExpressNotifyVO;
import com.ets.delivery.application.app.thirdservice.response.yunda.YundaOrderProcessVO;
import com.ets.delivery.application.app.thirdservice.response.yundaOpen.YundaOpenExpressNotifyVO;
import com.ets.delivery.application.common.bo.yunda.YundaSignBO;
import com.ets.delivery.application.common.consts.express.ExpressCodeEnum;
import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import com.ets.delivery.application.common.consts.yunda.YundaSourceOrderEnum;
import com.ets.delivery.application.common.dto.express.ExpressNotifyDTO;
import com.ets.delivery.application.common.dto.logistics.LogisticsOrderConfirmDTO;
import com.ets.delivery.application.common.dto.logistics.LogisticsOrderProcessDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.*;

import jakarta.validation.Valid;


@Slf4j
@RestController
@RequestMapping("/notify/yunda")
public class YundaController {

    @Autowired
    private LogisticsBusiness logisticsBusiness;

    @Autowired
    private ExpressBusiness expressBusiness;

    @Autowired
    private StockNotifyBusiness stockNotifyBusiness;

    @Autowired
    private YundaBusiness yundaBusiness;

    @RequestMapping(value = "/deliveryConfirm",
            consumes = MediaType.APPLICATION_XML_VALUE,
            produces = MediaType.APPLICATION_XML_VALUE
    )
    public DeliveryConfirmVO deliveryConfirm(@RequestBody String body,
                                             @RequestParam(value = "method", required = false) String method,
                                             @RequestParam(value = "timestamp", required = false) String timestamp,
                                             @RequestParam(value = "format", required = false) String format,
                                             @RequestParam(value = "app_key", required = false) String appKey,
                                             @RequestParam(value = "v", required = false) String v,
                                             @RequestParam(value = "sign", required = false) String sign,
                                             @RequestParam(value = "sign_method", required = false) String signMethod,
                                             @RequestParam(value = "customerId", required = false) String customerId) {
        try {
            // 签名校验
            YundaSignBO signBO = new YundaSignBO();
            signBO.setMethod(method).setTimestamp(timestamp).setFormat(format).setAppKey(appKey).setV(v).setSign(sign).setSignMethod(signMethod).setCustomerId(customerId).setBody(body);
            yundaBusiness.checkSign(signBO);

            DeliveryConfirmDTO deliveryConfirmDTO = yundaBusiness.parseResponse(body, DeliveryConfirmDTO.class);
            log.info("【订单发货确认】【韵达】{}", deliveryConfirmDTO);

            Integer orderSource = 1;
            if (ObjectUtils.isNotEmpty(deliveryConfirmDTO.getDeliveryOrder().getSourceOrder())) {
                orderSource = YundaSourceOrderEnum.orderTypeMap.getOrDefault(deliveryConfirmDTO.getDeliveryOrder().getSourceOrder(), 1);
            }

            LogisticsOrderConfirmDTO confirmDTO = new LogisticsOrderConfirmDTO();
            confirmDTO.setStorageCode(StorageCodeEnum.YUNDA.getValue());
            confirmDTO.setOrderSource(orderSource);
            confirmDTO.setOrderConfirmData(deliveryConfirmDTO);
            confirmDTO.setRawData(body);
            logisticsBusiness.orderConfirm(confirmDTO);
        } catch (Exception e) {
            log.error("【订单发货确认】【韵达】异常：{}", e.getMessage());
        }

        return new DeliveryConfirmVO();
    }

    @RequestMapping(value = "/expressPush",
            consumes = MediaType.APPLICATION_XML_VALUE,
            produces = MediaType.APPLICATION_XML_VALUE
    )
    public YundaExpressNotifyVO express(@RequestBody @Valid YundaExpressNotifyDTO notifyDTO) {
        log.info("【物流轨迹推送】【韵达】{}", notifyDTO);
        YundaExpressNotifyVO notifyVO = new YundaExpressNotifyVO();

        try {
            ExpressNotifyDTO expressNotifyDTO = new ExpressNotifyDTO();
            expressNotifyDTO.setExpressCode(ExpressCodeEnum.YUNDA.getValue());
            expressNotifyDTO.setNotifyData(notifyDTO);

            expressBusiness.expressNotify(expressNotifyDTO);
        } catch (Exception e) {
            log.error("【物流轨迹推送】异常：{}", e.getMessage());
            notifyVO.setFlag("fail");
        }
        return notifyVO;
    }

    @RequestMapping(value = "/orderProcess",
            consumes = MediaType.APPLICATION_XML_VALUE,
            produces = MediaType.APPLICATION_XML_VALUE
    )
    public YundaOrderProcessVO orderProcess(@RequestBody @Valid YundaOrderProcessDTO processDTO) {
        log.info("【订单流水通知】【韵达】{}", processDTO);
        YundaOrderProcessVO processVO = new YundaOrderProcessVO();

        try {
            LogisticsOrderProcessDTO orderProcessDTO = new LogisticsOrderProcessDTO();
            orderProcessDTO.setStorageCode(StorageCodeEnum.YUNDA.getValue());
            orderProcessDTO.setOrderProcessData(processDTO);

            logisticsBusiness.orderProcess(orderProcessDTO);
        } catch (Exception e) {
            log.error("【订单流水通知】【韵达】{}", e.getLocalizedMessage());
            processVO.setFlag("fail");
        }

        return processVO;
    }

    @PostMapping("/expressNotify")
    public YundaOpenExpressNotifyVO expressNotify(@RequestBody @Valid YundaOpenExpressNotifyDTO notifyDTO) {
        log.info("【物流轨迹推送】【韵达开放平台】{}", notifyDTO);
        YundaOpenExpressNotifyVO notifyVO = new YundaOpenExpressNotifyVO();

        try {
            ExpressNotifyDTO expressNotifyDTO = new ExpressNotifyDTO();
            expressNotifyDTO.setExpressCode(ExpressCodeEnum.YUNDA_OPEN.getValue());
            expressNotifyDTO.setNotifyData(notifyDTO);

            expressBusiness.expressNotify(expressNotifyDTO);
        } catch (Exception e) {
            log.error("【物流轨迹推送】异常：{}", e.getMessage());
            notifyVO.setResult(false);
            notifyVO.setMessage("物流推送异常");
            return notifyVO;
        }

        YundaOpenExpressNotifyVO.ResultData data = new YundaOpenExpressNotifyVO.ResultData();
        data.setOrderid(notifyDTO.getData().getOrderid());
        notifyVO.setData(data);
        return notifyVO;
    }

    @GetMapping("/expressNotify")
    public String expressNotify() {
        return "success";
    }


    @RequestMapping(value = "/entryOrderConfirm",
            consumes = MediaType.APPLICATION_XML_VALUE,
            produces = MediaType.APPLICATION_XML_VALUE
    )
    public StockResponseVO entryOrderConfirm(@RequestBody StockInConfirmDTO dto) {
        log.info("【入库单确认】【韵达】{}", dto);
        // 入库单状态变更通知接口
        stockNotifyBusiness.stockInNotify(dto);

        return new StockResponseVO();
    }

    @RequestMapping(value = "/stockOutConfirm",
            consumes = MediaType.APPLICATION_XML_VALUE,
            produces = MediaType.APPLICATION_XML_VALUE
    )
    public StockResponseVO stockOutConfirm(@RequestBody StockOutConfirmDTO dto) {
        log.info("【出库单确认】【韵达】{}", dto);
        // 出库单状态变更通知接口
        stockNotifyBusiness.stockOutNotify(dto);

        return new StockResponseVO();
    }
}
