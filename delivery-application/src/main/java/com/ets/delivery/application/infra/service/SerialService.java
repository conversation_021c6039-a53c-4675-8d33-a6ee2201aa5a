package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.delivery.application.common.dto.serial.AdminSerialListDTO;
import com.ets.delivery.application.infra.entity.Serial;
import com.ets.delivery.application.infra.mapper.SerialMapper;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.time.LocalTime;
import java.util.ArrayList;
import java.util.List;

/**
 * <p>
 * 序列号记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-27
 */
@Service
@DS("db-delivery")
public class SerialService extends BaseService<SerialMapper, Serial> {

    /**
     * 批量查询序列号
     */
    public List<Serial> getBySerialNos(List<String> serialNos) {
        if (ObjectUtils.isEmpty(serialNos)) {
            return new ArrayList<>();
        }
        return this.list(Wrappers.<Serial>lambdaQuery().in(Serial::getSerialNo, serialNos));
    }

    /**
     * 分页查询序列号列表
     */
    public IPage<Serial> getPage(AdminSerialListDTO listDTO) {
        Wrapper<Serial> wrapper = Wrappers.<Serial>lambdaQuery()
                .eq(StringUtils.isNotEmpty(listDTO.getSerialNo()), Serial::getSerialNo, listDTO.getSerialNo())
                .eq(StringUtils.isNotEmpty(listDTO.getStorageCode()), Serial::getStorageCode, listDTO.getStorageCode())
                .eq(StringUtils.isNotEmpty(listDTO.getStorageSku()), Serial::getStorageSku, listDTO.getStorageSku())
                .eq(ObjectUtils.isNotEmpty(listDTO.getInventoryType()), Serial::getInventoryType, listDTO.getInventoryType())
                .eq(ObjectUtils.isNotEmpty(listDTO.getStatus()), Serial::getStatus, listDTO.getStatus())
                .ge(ObjectUtils.isNotEmpty(listDTO.getCreateStartTime()), Serial::getCreatedAt,
                        ObjectUtils.isNotEmpty(listDTO.getCreateStartTime()) ? listDTO.getCreateStartTime().atTime(LocalTime.MIN) : null)
                .le(ObjectUtils.isNotEmpty(listDTO.getCreateEndTime()), Serial::getCreatedAt,
                        ObjectUtils.isNotEmpty(listDTO.getCreateEndTime()) ? listDTO.getCreateEndTime().atTime(LocalTime.MAX) : null)
                .orderByDesc(Serial::getCreatedAt);
        return this.baseMapper.selectPage(new Page<>(listDTO.getPageNum(), listDTO.getPageSize()), wrapper);
    }
}