package com.ets.delivery.application.controller.frontend;

import com.ets.common.JsonResult;
import com.ets.common.annotation.CosSignAnnotation;
import com.ets.delivery.application.app.business.storageMina.AuthBusiness;
import com.ets.delivery.application.app.business.storageMina.StorageBusiness;
import com.ets.delivery.application.app.business.storageMina.UserBusiness;
import com.ets.delivery.application.common.annotation.MinaCheckAccessAnnotation;
import com.ets.delivery.application.common.vo.storage.StorageVO;
import com.ets.delivery.application.common.vo.user.UserDetailVO;
import com.ets.delivery.application.infra.entity.User;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/frontend/user")
@MinaCheckAccessAnnotation
public class FrontendUserController {

    @Autowired
    private UserBusiness userBusiness;

    @Autowired
    private StorageBusiness storageBusiness;

    @Autowired
    private AuthBusiness authBusiness;

    @PostMapping("/getUserInfo")
    public JsonResult<UserDetailVO> getUserInfo() {

        return JsonResult.ok(userBusiness.getLoginUserInfo());
    }

    @PostMapping("/getStorageList")
    @CosSignAnnotation
    public JsonResult<List<StorageVO>> getStorageList() {

        User user = authBusiness.getLoginUser();

        return JsonResult.ok(storageBusiness.getListByCodeStr(user.getStorageCode()));
    }

}
