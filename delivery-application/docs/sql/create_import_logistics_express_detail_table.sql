-- 创建发货快递单导入详情表 (在db-delivery库中创建)
CREATE TABLE `etc_import_logistics_express_detail` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `batch_no` varchar(50) NOT NULL COMMENT '批次号',
  `logistics_sn` varchar(50) NOT NULL COMMENT '发货流水号',
  `order_sn` varchar(50) NOT NULL COMMENT '订单号',
  `express_company` varchar(20) NOT NULL COMMENT '快递公司',
  `express_number` varchar(30) NOT NULL COMMENT '快递单号',
  `result_level` int(11) NOT NULL DEFAULT '0' COMMENT '分析结果等级[0-正常 1-警告 2-错误]',
  `result_msg` text COMMENT '分析结果说明',
  `status` int(11) NOT NULL DEFAULT '0' COMMENT '状态[-1-删除 0-正常]',
  `record_status` int(11) NOT NULL DEFAULT '0' COMMENT '记录状态[0-初始化 1-已保存 2-已推送]',
  `created_at` datetime NOT NULL COMMENT '创建时间',
  `updated_at` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_batch_no` (`batch_no`),
  KEY `idx_logistics_sn` (`logistics_sn`),
  KEY `idx_order_sn` (`order_sn`),
  KEY `idx_express_number` (`express_number`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='发货快递单导入详情表';