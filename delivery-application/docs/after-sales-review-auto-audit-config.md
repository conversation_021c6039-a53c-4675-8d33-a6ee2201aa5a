# 售后审核自动审核开关配置说明

## 功能概述

售后审核自动审核功能支持两级开关控制：
1. **总开关** - 控制整个自动审核功能的开启/关闭
2. **卡方维度开关** - 针对不同发卡方（issuerId）的精细化控制

## 配置说明

### 配置位置
配置项位于独立的配置文件中，通过 Nacos 配置中心管理。

### 配置项说明

#### 配置结构
```yaml
after-sales-review-auto-audit:
  # 售后审核自动审核总开关
  # 0: 关闭 - 所有审核单直接自动审核通过
  # 1: 开启 - 执行后续的卡方维度开关判断
  total-switch: 1

  # 售后审核自动审核卡方维度开关配置
  # key: 发卡方ID (issuerId)
  # value: 0-关闭该卡方的数据比对，直接通过; 1-开启该卡方的数据比对
  issuer-switch:
    1: 1    # 江苏-苏通卡: 开启数据比对
    2: 0    # 北京-速通卡: 关闭数据比对，直接通过
    10: 1   # 天津-速通卡: 开启数据比对
    25: 0   # 广西-八桂行卡: 关闭数据比对，直接通过
```

## 逻辑说明

### 开关判断逻辑
1. **总开关关闭** (`total-switch: 0`)
   - 所有审核单直接自动审核通过
   - 不执行数据比对逻辑
   - 审核备注：`总开关关闭，自动审核通过`

2. **总开关开启，卡方开关关闭** (`total-switch: 1` 且对应 `issuerId` 的值为 `0`)
   - 该发卡方的审核单直接自动审核通过
   - 不执行数据比对逻辑
   - 审核备注：`发卡方[issuerId]开关关闭，自动审核通过`

3. **总开关开启，卡方开关开启** (`total-switch: 1` 且对应 `issuerId` 的值为 `1`)
   - 执行原有的行驶证数据比对逻辑
   - 数据一致：自动审核通过，审核备注：`数据比对自动审核通过`
   - 数据不一致：需要人工审核，记录不一致信息到日志

4. **卡方开关未配置**
   - 如果某个发卡方ID在 `issuer-switch` 中未配置
   - 默认执行数据比对逻辑（相当于开关开启）

## 配置示例

### 示例1：全部关闭自动审核
```yaml
after-sales-review-auto-audit:
  total-switch: 0
  # 总开关关闭时，卡方开关配置无效
```

### 示例2：部分卡方关闭数据比对
```yaml
after-sales-review-auto-audit:
  total-switch: 1
  issuer-switch:
    1: 1    # 江苏-苏通卡: 执行数据比对
    2: 0    # 北京-速通卡: 直接通过
    10: 1   # 天津-速通卡: 执行数据比对
    25: 0   # 广西-八桂行卡: 直接通过
    # 其他未配置的发卡方默认执行数据比对
```

### 示例3：全部执行数据比对
```yaml
after-sales-review-auto-audit:
  total-switch: 1
  # 不配置卡方开关或全部设置为1，所有发卡方都执行数据比对
```

## 注意事项

1. **配置热更新**: 配置支持通过 Nacos 热更新，无需重启应用
2. **默认值**: 总开关默认值为 `1`（开启）
3. **配置类**: 使用独立的配置类 `AfterSalesReviewAutoAuditConfig` 管理
4. **日志记录**: 所有自动审核操作都会记录到 `aftersales_reviews_log` 表中
5. **回调通知**: 自动审核通过后会发送回调通知给相关系统
6. **兼容性**: 如果配置项不存在，系统会使用默认值，保证向后兼容

## 发卡方ID对照表

| 发卡方ID | 发卡方名称 |
|---------|-----------|
| 1 | 江苏-苏通卡 |
| 2 | 北京-速通卡 |
| 10 | 天津-速通卡 |
| 25 | 广西-八桂行卡 |
| 26 | 青海-青通卡 |
| 28 | 内蒙古-蒙通卡 |
| 30 | 青海中远-青通卡 |
| 31 | 北京货车-速通卡 |
| 32 | 江苏货车-苏通卡 |
| 33 | 内蒙货车-蒙通卡 |
| 35 | 苏通卡-出租车 |
| 37 | 天津货车-速通卡 |
| 38 | 网路智联-客车 |
| 39 | 江苏9901-苏通卡 |
| 98 | 江苏货车-运政卡 |

*注：具体的发卡方ID和名称请参考系统中的实际配置*
