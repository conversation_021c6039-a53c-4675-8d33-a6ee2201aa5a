# 发货快递单导入功能说明

## 功能概述
发货快递单导入功能允许用户通过Excel文件批量导入发货快递单信息，包含发货流水号、订单号、快递公司和快递单号等关键信息。

## Excel模板格式

### 必需字段
| 列名 | 字段说明 | 数据类型 | 是否必填 | 最大长度 | 备注 |
|------|----------|----------|----------|----------|------|
| 发货流水号 | 发货单的唯一流水号 | 文本 | 是 | 50字符 | 用于标识唯一的发货单 |
| 订单号 | 关联的订单编号 | 文本 | 是 | 50字符 | 业务订单的编号 |
| 快递公司 | 承运的快递公司名称 | 文本 | 是 | 20字符 | 如：顺丰、圆通、中通等 |
| 快递单号 | 快递公司的运单号 | 文本 | 是 | 30字符 | 用于快递跟踪的单号，只能包含字母和数字 |

### Excel文件要求
- 文件格式：支持 `.xls` 和 `.xlsx` 格式
- 表头位置：第2行为表头（第1行可以是标题）
- 数据行数：最少1条，最多1000条记录
- 编码格式：UTF-8

### 示例数据
```
发货流水号          订单号              快递公司    快递单号
SF202509040001     ORD202509040001     顺丰       SF1234567890123
YT202509040002     ORD202509040002     圆通       YT9876543210987
ZT202509040003     ORD202509040003     中通       ZT1122334455667
```

## 导入流程
1. 准备符合模板格式的Excel文件
2. 通过系统上传Excel文件
3. 系统自动校验数据格式和完整性
4. 显示导入结果，包括成功、警告和错误记录
5. 确认导入数据

## 数据校验规则

### 错误级别校验（导入失败）
- 发货流水号不能为空
- 订单号不能为空
- 快递公司不能为空
- 快递单号不能为空
- 发货流水号长度不能超过50个字符
- 订单号长度不能超过50个字符
- 快递公司名称长度不能超过20个字符
- 快递单号长度不能超过30个字符
- 快递单号格式错误，只能包含字母和数字（正则：^[A-Za-z0-9]+$）

### 警告级别校验（可以导入但需注意）
- 文件中存在相同的发货流水号

## 导入结果说明
- **正常(3)**：数据格式正确，可以正常导入
- **警告(2)**：数据可以导入，但存在需要注意的问题
- **错误(1)**：数据格式错误，无法导入，需要修正后重新上传

## 注意事项
1. 确保Excel文件中的数据准确无误
2. 发货流水号在文件中不能重复
3. 建议在正式导入前先进行小批量测试
4. 导入过程中请勿关闭浏览器或刷新页面
5. 如遇到问题，请联系系统管理员

## 数据库配置
- **数据库**: db-delivery
- **表名**: etc_import_delivery_detail
- **数据源注解**: @DS("db-delivery")

## 相关枚举类型
- 导入类型：`deliveryImport`
- 导入状态：初始化(0)、已保存(1)、已推送(2)
- 结果级别：正常(3)、警告(2)、错误(1) - 使用通用枚举 `ImportResultLevelEnum`